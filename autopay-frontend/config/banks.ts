import { BsArrowDownRightCircle } from 'react-icons/bs'
import { FaArrowRightArrowLeft } from 'react-icons/fa6'
import { VscAccount } from 'react-icons/vsc'

// Re-export types from backend API
export type { BankConfig as Bank, BankConnectionField as BankFieldRequirement } from '@/lib/types/banks'

// Legacy static data - kept for fallback purposes
export const connectionFields = [
  {
    name: 'accountNumber',
    title: 'Số tài khoản',
    description: 'Số tài khoản của bạn tại ngân hàng',
    minLength: 3,
  },
  {
    name: 'accountName',
    title: 'Tên tài khoản',
    description: 'Tên tài khoản của bạn tại ngân hàng',
    minLength: 3,
  },
  {
    name: 'idCardNumber',
    title: 'CCCD/CMT',
    description: 'Số CCCD/CMT đã dùng để đăng ký tài khoản ngân hàng',
    minLength: 9,
  },
  {
    name: 'phoneNumber',
    title: '<PERSON><PERSON> điện thoại',
    description: '<PERSON><PERSON> điện thoại đã dùng để đăng ký tài khoản ngân hàng',
    minLength: 9,
  },
]

export const banks = [
  {
    code: 'ocb',
    name: 'OceanBank',
    icon: '/banks/ocb-icon.png',
    speed: '1-5 giây',
    register: ['Online'],
    virtualAccount: true,
    virtualAccountPrefix: 'ITG',
    promotion: '',
    stability: '99.99%',
    connectionFields: ['accountNumber', 'idCardNumber', 'phoneNumber'],
    introduction:
      'Ngân hàng thương mại cổ phần Phương Đông, còn được gọi Oricombank hay OCB, là một Ngân hàng thương mại cổ phần tại Việt Nam.',
    supports: [
      {
        support: 'Đồng bộ tiền vào tài khoản ảo (VA)',
        icon: BsArrowDownRightCircle,
      },
      {
        support: 'Hỗ trợ tạo tài khoản ảo (VA)',
        icon: VscAccount,
      },
    ],
    notes: [
      '- Đồng bộ khi giao dịch vào **tài khoản ảo (VA)**',
      '- Hỗ trợ tạo **tài khoản ảo (VA)**',
      '- Tốc độ đồng bộ giao dịch từ 1 đến 5 giây',
      // '- [API chính thức](#) từ OCB',
      '- ',
      '- *Chưa hỗ trợ đồng bộ giao dịch nhận tiền vào trực tiếp tài khoản chính, quý khách cần nhận tiền qua VA*',
      '- *Chưa hỗ trợ đồng bộ tiền ra*',
      '- *Chưa hỗ trợ hiện số dư tài khoản*',
    ],
  },
  {
    code: 'mb',
    name: 'MBBank',
    icon: '/banks/mb-icon.png',
    speed: '1-5 giây',
    register: ['Online'],
    virtualAccount: true,
    virtualAccountPrefix: '',
    promotion: '',
    stability: '99.99%',
    connectionFields: ['accountNumber', 'accountName', 'idCardNumber', 'phoneNumber'],
    introduction:
      'Ngân hàng Thương mại Cổ phần Quân đội, gọi tắt là Ngân hàng Quân đội, viết tắt là MB, là một ngân hàng thương mại cổ phần của Việt Nam, một doanh nghiệp trực thuộc Bộ Quốc phòng.',
    supports: [
      {
        support: 'Đồng bộ tiền vào, tiền ra tài khoản chính',
        icon: FaArrowRightArrowLeft,
      },
      {
        support: 'Hỗ trợ tạo tài khoản ảo (VA)',
        icon: VscAccount,
      },
    ],
    notes: [
      '- Đồng bộ khi giao dịch vào **tài khoản chính** và **tài khoản ảo (VA)**',
      '- Hỗ trợ đồng bộ **tiền vào, tiền ra**',
      '- Hỗ trợ tạo **tài khoản ảo (VA)**',
      '- Tốc độ đồng bộ giao dịch từ 1 đến 5 giây',
      // '- [API chính thức](#) từ OCB',
      '- ',
      '- *Chưa hỗ trợ hiện số dư tài khoản*',
    ],
  },
  {
    code: 'klb',
    name: 'KienLongBank',
    icon: '/banks/klb-icon.png',
    speed: '1-5 giây',
    register: ['Online'],
    virtualAccount: true,
    virtualAccountPrefix: '',
    promotion: '',
    stability: '99.99%',
    connectionFields: ['accountNumber'],
    introduction: 'Ngân hàng TMCP Kiên Long là một ngân hàng thương mại cổ phần tại Việt Nam.',
    supports: [
      {
        support: 'Đồng bộ tiền vào tài khoản ảo (VA)',
        icon: BsArrowDownRightCircle,
      },
      {
        support: 'Hỗ trợ tạo tài khoản ảo (VA)',
        icon: VscAccount,
      },
    ],
    notes: [
      '- Đồng bộ khi giao dịch vào **tài khoản ảo (VA)**',
      '- Hỗ trợ tạo **tài khoản ảo (VA)**',
      '- Tốc độ đồng bộ giao dịch từ 1 đến 5 giây',
      // '- [API chính thức](#) từ OCB',
      '- ',
      '- *Chưa hỗ trợ đồng bộ giao dịch nhận tiền vào trực tiếp tài khoản chính, quý khách cần nhận tiền qua VA*',
      '- *Chưa hỗ trợ đồng bộ tiền ra*',
      '- *Chưa hỗ trợ hiện số dư tài khoản*',
    ],
  },
]
