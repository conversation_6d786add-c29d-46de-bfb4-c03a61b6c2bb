// Bank configuration types from backend API

export interface BankConnectionField {
  name: string
  title: string
  description: string
  min_length: number
}

export interface BankSupportsDisplay {
  support: string
  icon: string
}

export interface BankUIConfig {
  icon: string
  speed: string
  register: string[]
  virtual_account: boolean
  virtual_account_prefix: string
  promotion: string
  stability: string
  introduction: string
  supports_display: BankSupportsDisplay[]
  notes: string[]
}

export interface BankSupports {
  virtual_account: boolean
  balance_check: boolean
  transaction_history: boolean
  account_validation: boolean
  transaction_sync: boolean
  virtual_account_transactions: boolean
  account_linking?: boolean
}

export interface BankConfig {
  code: string
  name: string
  display_name: string
  connection_fields: string[]
  connection_fields_config?: BankConnectionField[]
  features: string[]
  supports: BankSupports
  ui_config: BankUIConfig
}

export interface BanksConfigResponse {
  banks: BankConfig[]
  connection_fields: Record<string, BankConnectionField>
}
