// Bank configuration types from backend API

export interface BankConnectionField {
  name: string
  title: string
  description: string
  min_length: number
}

export interface BankSupportsDisplay {
  support: string
  icon: string
}

export interface BankUIConfig {
  icon: string
  speed: string
  register: string[]
  virtual_account: boolean
  virtual_account_prefix: string
  promotion: string
  stability: string
  introduction: string
  supports_display: BankSupportsDisplay[]
  notes: string[]
}

export interface BankSupports {
  virtual_account: boolean
  balance_check: boolean
  transaction_history: boolean
  account_validation: boolean
  transaction_sync: boolean
  virtual_account_transactions: boolean
  account_linking?: boolean
}

export interface BankConfig {
  code: string
  name: string
  display_name: string
  connection_fields: string[]
  connection_fields_config?: BankConnectionField[]
  features: string[]
  supports: BankSupports
  ui_config: BankUIConfig
}

export interface BanksConfigResponse {
  banks: BankConfig[]
  connection_fields: Record<string, BankConnectionField>
}

// Legacy types for backward compatibility with existing frontend code
export interface LegacyBank {
  code: string
  name: string
  icon: string
  speed: string
  register: string[]
  virtualAccount: boolean
  virtualAccountPrefix: string
  promotion: string
  stability: string
  connectionFields: string[]
  introduction: string
  supports: BankSupportsDisplay[]
  notes: string[]
}

export interface LegacyBankFieldRequirement {
  name: string
  title: string
  description: string
  minLength: number
}

// Utility functions to convert between new and legacy formats
export function convertToLegacyFormat(banksConfig: BanksConfigResponse): {
  banks: LegacyBank[]
  connectionFields: LegacyBankFieldRequirement[]
} {
  const banks: LegacyBank[] = banksConfig.banks.map(bank => ({
    code: bank.code,
    name: bank.name,
    icon: bank.ui_config.icon,
    speed: bank.ui_config.speed,
    register: bank.ui_config.register,
    virtualAccount: bank.ui_config.virtual_account,
    virtualAccountPrefix: bank.ui_config.virtual_account_prefix,
    promotion: bank.ui_config.promotion,
    stability: bank.ui_config.stability,
    connectionFields: bank.connection_fields,
    introduction: bank.ui_config.introduction,
    supports: bank.ui_config.supports_display,
    notes: bank.ui_config.notes,
  }))

  const connectionFields: LegacyBankFieldRequirement[] = Object.values(banksConfig.connection_fields).map(field => ({
    name: field.name,
    title: field.title,
    description: field.description,
    minLength: field.min_length,
  }))

  return { banks, connectionFields }
}
