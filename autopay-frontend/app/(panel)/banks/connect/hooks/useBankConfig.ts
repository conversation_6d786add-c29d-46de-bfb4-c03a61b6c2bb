import { useQuery } from '@tanstack/react-query'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import type { BankConfig, BanksConfigResponse } from '@/lib/types/banks'

/**
 * Hook to fetch all banks configuration
 */
export function useBanksConfig() {
  return useQuery({
    queryKey: ['banks', 'config'],
    queryFn: async (): Promise<BanksConfigResponse> => {
      const response = await queryFetchHelper('/banks/config')
      return response.data
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch specific bank configuration
 */
export function useBankConfig(bankCode: string) {
  return useQuery({
    queryKey: ['banks', 'config', bankCode],
    queryFn: async (): Promise<BankConfig> => {
      const response = await queryFetchHelper(`/banks/config/${bankCode}`)
      return response.data
    },
    enabled: !!bankCode,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}
