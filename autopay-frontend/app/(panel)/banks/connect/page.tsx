'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { BiSolidBusiness } from 'react-icons/bi'
import { CgFileDocument } from 'react-icons/cg'
import { FaPerson } from 'react-icons/fa6'
import { MdOutlineTipsAndUpdates } from 'react-icons/md'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Sheet, SheetClose, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import type { BankConfig, BanksConfigResponse } from '@/lib/types/banks'
import { fetchHelper } from '@/lib/utils/fetchHelper'
import Image from 'next/image'

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/custom-ui/table'
import { <PERSON>Beam } from '@/components/ui/animated-beam'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { GoQuestion } from 'react-icons/go'

import { forwardRef, useRef } from 'react'
import { IoMdArrowRoundBack } from 'react-icons/io'

import { cn } from '@/lib/utils'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import * as arrowData from '@/assets/lottie/arrow-right.json'
import { toastOnSelector } from '@/lib/utils/toastOnSelector'
import dynamic from 'next/dynamic'
import { IoCheckbox } from 'react-icons/io5'
import { RiFileInfoLine } from 'react-icons/ri'
import ReactMarkdown from 'react-markdown'
import { useStore } from './stores/store'
const Lottie = dynamic(() => import('react-lottie-player'), { ssr: false })

const Circle = forwardRef<HTMLDivElement, { className?: string; children?: React.ReactNode }>(
  ({ className, children }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'bg-primary-foreground z-10 flex size-12 items-center justify-center rounded-full border-2 p-3 shadow-[0_0_16px_-12px_rgba(0,0,0,0.8)]',
          className
        )}>
        {children}
      </div>
    )
  }
)

Circle.displayName = 'Circle'

import { ScrollArea } from '@/components/ui/scroll-area'
import { toast } from 'sonner'

type Bank = {
  code: string
  name: string
}

// Icon mapping for supports display
const iconMapping = {
  BsArrowDownRightCircle: () => <span>↓</span>,
  FaArrowRightArrowLeft: () => <span>↔</span>,
  VscAccount: () => <span>👤</span>,
}
const RenderSheet = ({ bankCode }: { bankCode: string }) => {
  // Dynamic import for bank sheet components
  const SheetComponent = React.useMemo(() => {
    return React.lazy(() =>
      import(`./sheets/${bankCode}`).catch(() => ({ default: () => <div>Component not found</div> }))
    )
  }, [bankCode])

  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <SheetComponent bankCode={bankCode} />
    </React.Suspense>
  )
}

export default function Component() {
  // State for banks data from backend
  const [banks, setBanks] = useState<BankConfig[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Animation
  const containerRef = useRef<HTMLDivElement>(null)
  const div1Ref = useRef<HTMLDivElement>(null)
  const div2Ref = useRef<HTMLDivElement>(null)

  const { bankCode, setBankCode, isShowRegisterForm, setIsShowRegisterForm } = useStore()

  // Fetch banks data from backend
  useEffect(() => {
    const fetchBanks = async () => {
      try {
        setIsLoading(true)
        const response = await fetchHelper('/banks/config')
        if (response.success) {
          setBanks(response.data.banks)
        } else {
          setError('Không thể tải danh sách ngân hàng')
        }
      } catch (err) {
        setError('Có lỗi xảy ra khi tải danh sách ngân hàng')
        console.error('Error fetching banks:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchBanks()
  }, [])

  const showRegisterForm = () => {
    if (!bankCode) {
      return toastOnSelector({
        selectorRef: buttonRef,
        message: 'Vui lòng chọn một ngân hàng để kết nối.',
      })
    }

    setIsShowRegisterForm(true)
  }

  const selectBank = (bank: BankConfig) => {
    toast.dismiss()
    toast.success('Đã chọn ' + bank.name.toUpperCase(), {
      style: {
        width: 'fit-content',
        padding: '5px 10px',
        left: 0,
        right: 0,
        margin: '0 auto',
      },
    })
    setBankCode(bank.code)
  }

  return (
    <>
      <PageHeading
        backIcon
        title="Kết nối tài khoản ngân hàng"
        rightSide={
          <div className="hidden items-center gap-2 md:ml-auto md:flex">
            <Button
              size="sm"
              variant="outline"
              asChild>
              <Link
                href="https://docs.autopay.vn"
                target="_blank"
                className="flex items-center gap-1">
                <CgFileDocument className="size-3" />
                Tài liệu hướng dẫn
              </Link>
            </Button>
          </div>
        }
      />

      <div
        className="relative flex w-full items-center justify-center font-semibold"
        ref={containerRef}>
        <div className="flex w-full flex-col items-stretch justify-between gap-10 overflow-hidden md:w-1/2">
          <div className="flex flex-row justify-between">
            <div className="z-10 flex flex-col gap-2">
              <Circle ref={div1Ref}>1</Circle>
              Chọn loại tài khoản
            </div>
            <div className="z-10 flex flex-col items-end gap-2">
              <Circle ref={div2Ref}>2</Circle>
              Kết nối tài khoản
            </div>
          </div>
        </div>
        <AnimatedBeam
          duration={3}
          containerRef={containerRef}
          fromRef={div1Ref}
          toRef={div2Ref}
        />
      </div>

      <Card className="py-4 pt-0">
        <CardContent className="p-4 pb-0">
          <Tabs defaultValue="personal">
            <TabsList>
              <TabsTrigger
                value="personal"
                className="flex items-center gap-1">
                <FaPerson className="h-4 w-4" />
                Tài khoản cá nhân
              </TabsTrigger>
              <TabsTrigger
                value="business"
                className="flex items-center gap-1">
                <BiSolidBusiness className="h-4 w-4" />
                Tài khoản doanh nghiệp
              </TabsTrigger>
            </TabsList>
            <TabsContent
              value="personal"
              className="mt-4">
              <Table
                className="font-semibold"
                wrapperClassName="overflow-x-auto">
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-40">
                      <span className="flex items-center gap-1">
                        Ngân hàng
                        <GoQuestion
                          className="size-4"
                          data-tooltip-html="Ngân hàng hỗ trợ open banking mà bạn đang sử dụng."
                        />
                      </span>
                    </TableHead>
                    <TableHead className="min-w-40">
                      <span className="flex items-center gap-1">
                        Tốc độ cập nhật
                        <GoQuestion
                          className="size-4"
                          data-tooltip-html="Tốc độ cập nhật giao dịch từ ngân hàng sau khi phát sinh giao dịch."
                        />
                      </span>
                    </TableHead>
                    <TableHead className="min-w-40">
                      <span className="flex items-center gap-1">
                        Thủ tục kết nối
                        <GoQuestion
                          className="size-4"
                          data-tooltip-html="Thủ tục kết nối tài khoản ngân hàng lên hệ thống."
                        />
                      </span>
                    </TableHead>
                    <TableHead className="min-w-40">
                      <span className="flex items-center gap-1">
                        Khuyến mãi
                        <GoQuestion
                          className="size-4"
                          data-tooltip-html="Các chính sách ưu đãi khi mở tài khoản ngân hàng & sử dụng dịch vụ trên hệ thống."
                        />
                      </span>
                    </TableHead>
                    <TableHead className="min-w-72">
                      <span className="flex items-center gap-1">
                        Hỗ trợ đồng bộ
                        <GoQuestion
                          className="size-4"
                          data-tooltip-html="Tính năng đồng bộ và hỗ trợ tài khoản tùy thuộc vào từng ngân hàng"
                        />
                      </span>
                    </TableHead>
                    <TableHead className="flex min-w-40 items-center justify-center">Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {banks.map((bank) => (
                    <TableRow
                      data-tooltip-html={'Bấm để chọn ' + bank.name}
                      className="hover:bg-accent cursor-pointer"
                      key={bank.name}
                      onClick={() => selectBank(bank)}>
                      <TableCell className="flex items-center gap-1 px-2 py-4">
                        <Image
                          src={bank.icon}
                          alt="MBBank"
                          className="size-6"
                          width={48}
                          height={48}
                        />
                        {bank.name}
                        <span className="uppercase">({bank.code})</span>
                      </TableCell>
                      <TableCell>{bank.speed}</TableCell>
                      <TableCell>{bank.register.join(',')}</TableCell>
                      <TableCell>
                        <ReactMarkdown>{bank.promotion}</ReactMarkdown>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          {bank.supports.map((support, index) => {
                            return (
                              <span
                                key={index}
                                className="flex items-center gap-2">
                                {<support.icon />}
                                {support.support}
                              </span>
                            )
                          })}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center">
                          {bankCode !== bank.code ? (
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={() => setBankCode(bank.code)}>
                              Chọn
                            </Button>
                          ) : (
                            <IoCheckbox className="size-5" />
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
            <TabsContent
              value="business"
              className="mt-4">
              <Table className="font-semibold">
                <TableCaption className="mt-6">
                  <Alert>
                    <AlertDescription className="flex gap-2 text-left">
                      <RiFileInfoLine className="text-primary size-5" />
                      <div className="font-normal">
                        Hệ thống đang cập nhật tính năng kết nối ngân hàng doanh nghiệp. Vui lòng liên hệ với bộ phận hỗ
                        trợ của chúng tôi để được tư vấn.
                      </div>
                    </AlertDescription>
                  </Alert>
                </TableCaption>
              </Table>
            </TabsContent>
          </Tabs>
          <Alert className="mt-4">
            <AlertDescription className="flex gap-2 text-left">
              <MdOutlineTipsAndUpdates className="text-primary size-5 shrink-0" />
              <div className="font-normal">
                Hãy chọn một ngân hàng trong danh sách hỗ trợ, sau đó bấm
                <Badge
                  variant="outline"
                  className="mx-1">
                  Tiếp tục
                </Badge>
                để bắt đầu quá trình kết nối với tài khoản của bạn.
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>

        <CardFooter className="px-4">
          <Button
            className="w-full gap-2"
            onClick={() => showRegisterForm()}
            ref={buttonRef}>
            Tiếp tục
            <Lottie
              className="dark:invert"
              play
              animationData={arrowData}
              style={{ width: 20, height: 20 }}
            />
          </Button>
        </CardFooter>
      </Card>
      <Sheet open={isShowRegisterForm}>
        <SheetContent
          className="w-full md:max-w-lg"
          onPointerDownOutside={() => setIsShowRegisterForm(false)}
          onEscapeKeyDown={() => setIsShowRegisterForm(false)}>
          <SheetHeader className="bg-primary-foreground absolute inset-0 z-10 p-0">
            <div
              className="p-4"
              style={{
                backgroundImage:
                  'radial-gradient(circle 321px at 8.3% 75.7%,rgba(209,247,241,1) 0%,rgba(249,213,213,1) 81%)',
              }}>
              <div className="flex items-center justify-between">
                <SheetClose asChild>
                  <Button
                    variant="link"
                    className="dark:text-primary-foreground justify-start gap-1 pl-0"
                    onClick={() => setIsShowRegisterForm(false)}>
                    <IoMdArrowRoundBack className="size-5" /> Quay lại
                  </Button>
                </SheetClose>

                <Badge className="mx-1 uppercase">{bankCode}</Badge>
              </div>
              <SheetTitle className="flex justify-center py-5">
                <Image
                  src={`/banks/${bankCode}.png`}
                  alt={bankCode}
                  width={300}
                  height={50}
                />
              </SheetTitle>
              <SheetDescription />
            </div>
            <SheetDescription asChild>
              <ScrollArea className="h-[calc(100vh-200px)]">
                <div className="grid gap-4 p-4">
                  <RenderSheet bankCode={bankCode} />
                </div>
              </ScrollArea>
            </SheetDescription>
          </SheetHeader>
        </SheetContent>
      </Sheet>
    </>
  )
}
