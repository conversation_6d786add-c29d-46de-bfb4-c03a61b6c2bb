import type { BankConnectionField } from '@/lib/types/banks'
import { z } from 'zod'

export const createDynamicSchema = (configs: BankConnectionField[]) => {
  const schemaShape: Record<string, z.ZodSchema> = {}

  configs.forEach((field: BankConnectionField) => {
    let fieldSchema

    fieldSchema = z
      .string()
      .min(
        field.min_length,
        field.min_length > 1
          ? `${field.title} phải dài ít nhất ${field.min_length} ký tự`
          : `${field.title} là bắt buộc`
      )

    schemaShape[field.name] = fieldSchema
  })

  return z.object(schemaShape)
}

export const createDefaultValues = (configs: BankConnectionField[]) => {
  const defaults: Record<string, string> = {}
  configs.forEach((field: { name: string }) => {
    defaults[field.name] = ''
  })
  return defaults
}
