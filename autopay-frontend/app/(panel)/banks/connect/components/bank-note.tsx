import { Alert, AlertDescription } from '@/components/ui/alert'
import type { BankConfig } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import ReactMarkdown from 'react-markdown'
import { useStore } from '../stores/store'

export default function Component() {
  const { bankCode } = useStore()

  const { data: bank, isLoading } = useQuery({
    queryKey: ['banks', 'config', bankCode],
    queryFn: async (): Promise<BankConfig> => {
      const response = await queryFetchHelper(`/banks/config/${bankCode}`)
      return response.data
    },
    enabled: !!bankCode,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  if (isLoading || !bank) {
    return null
  }

  return (
    <Alert className="bg-accent/10">
      <AlertDescription className="text-muted-foreground flex gap-4">
        <ul>
          {bank?.ui_config.notes.map((note: string) => (
            <li key={note}>
              <ReactMarkdown
                components={{
                  ul: ({ children }) => <>{children}</>,
                  li(props) {
                    const { children } = props
                    return (
                      <div className={children ? '' : 'h-2'}>
                        {children ? '-' : null} {children}
                      </div>
                    )
                  },
                  a(props) {
                    const { children, ...rest } = props
                    return (
                      <a
                        {...rest}
                        className="text-blue-700">
                        {children}
                      </a>
                    )
                  },
                }}>
                {note}
              </ReactMarkdown>
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  )
}
