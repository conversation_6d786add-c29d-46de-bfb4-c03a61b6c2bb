import { Alert, AlertDescription } from '@/components/ui/alert'
import type { BankConfig } from '@/lib/types/banks'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { MdOutlineTipsAndUpdates } from 'react-icons/md'
import ReactMarkdown from 'react-markdown'

export default function Component({ bankCode }: { bankCode: string }) {
  const { data: bank, isLoading } = useQuery({
    queryKey: ['banks', 'config', bankCode],
    queryFn: async (): Promise<BankConfig> => {
      const response = await queryFetchHelper(`/banks/config/${bankCode}`)
      return response.data
    },
    enabled: !!bankCode,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  if (isLoading || !bank) {
    return null
  }

  return (
    bank.ui_config.introduction && (
      <Alert>
        <AlertDescription className="text-muted-foreground flex gap-4">
          <MdOutlineTipsAndUpdates className="text-primary h-6 w-6 shrink-0" />
          <div className="font-normal">
            <ReactMarkdown>{bank.ui_config.introduction}</ReactMarkdown>
          </div>
        </AlertDescription>
      </Alert>
    )
  )
}
