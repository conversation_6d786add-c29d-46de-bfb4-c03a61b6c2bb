'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import useAuth from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

const taxIdSchema = z.object({
  tax_id: z.string().max(50, 'Mã số thuế không được vượt quá 50 ký tự').optional(),
})

type TaxIdValues = z.infer<typeof taxIdSchema>

export function TaxIdForm() {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()

  // Fetch company data
  const { data: company, isLoading } = useQuery({
    queryKey: ['company', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/company`)
    },
    enabled: !!user?.current_organization?.id && !authLoading,
  })

  // Update company mutation
  const updateCompanyMutation = useMutation({
    mutationFn: async (data: TaxIdValues) => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/company`, {
        method: 'PUT',
        body: JSON.stringify(data),
      })
    },
    onMutate: () => {
      toast.loading('Đang cập nhật mã số thuế...', { id: 'tax-id-update' })
    },
    onSuccess: (response: any) => {
      toast.success(response.message || 'Cập nhật mã số thuế thành công', { id: 'tax-id-update' })
      queryClient.invalidateQueries({
        queryKey: ['company', user?.current_organization?.id],
      })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật mã số thuế', { id: 'tax-id-update' })
    },
  })

  const form = useForm<TaxIdValues>({
    resolver: zodResolver(taxIdSchema),
    defaultValues: {
      tax_id: '',
    },
    mode: 'onChange',
  })

  // Update form when company data is loaded
  useEffect(() => {
    if (company?.data) {
      form.reset({
        tax_id: company.data.tax_id || '',
      })
    }
  }, [company, form])

  const onSubmit = (data: TaxIdValues) => {
    updateCompanyMutation.mutate(data)
  }

  if (isLoading) {
    return <FormSkeleton />
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Mã số thuế</CardTitle>
        <CardDescription>
          Nếu bạn muốn hóa đơn của mình hiển thị một mã số thuế cụ thể, hãy nhập vào đây.
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4">
          <CardContent>
            <FormField
              control={form.control}
              name="tax_id"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="*********"
                      maxLength={50}
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              variant="default"
              size="sm"
              disabled={updateCompanyMutation.isPending}>
              {updateCompanyMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
