'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import useAuth from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

const companyNameSchema = z.object({
  full_name: z.string().max(64, 'Tên đầy đủ không được vượt quá 64 ký tự').optional(),
  short_name: z.string().max(64, 'Tên viết tắt không được vượt quá 64 ký tự').optional(),
})

type CompanyNameValues = z.infer<typeof companyNameSchema>

export function CompanyNameForm() {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()

  // Fetch company data
  const { data: company, isLoading } = useQuery({
    queryKey: ['company', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/company`)
    },
    enabled: !!user?.current_organization?.id && !authLoading,
  })

  // Update company mutation
  const updateCompanyMutation = useMutation({
    mutationFn: async (data: CompanyNameValues) => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/company`, {
        method: 'PUT',
        body: JSON.stringify(data),
      })
    },
    onMutate: () => {
      toast.loading('Đang cập nhật tên công ty...', { id: 'company-name-update' })
    },
    onSuccess: (response: any) => {
      toast.success(response.message || 'Cập nhật tên công ty thành công', { id: 'company-name-update' })
      queryClient.invalidateQueries({
        queryKey: ['company', user?.current_organization?.id],
      })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật tên công ty', { id: 'company-name-update' })
    },
  })

  const form = useForm<CompanyNameValues>({
    resolver: zodResolver(companyNameSchema),
    defaultValues: {
      full_name: '',
      short_name: '',
    },
    mode: 'onChange',
  })

  // Update form when company data is loaded
  useEffect(() => {
    if (company?.data) {
      form.reset({
        full_name: company.data.full_name || '',
        short_name: company.data.short_name || '',
      })
    }
  }, [company, form])

  const onSubmit = (data: CompanyNameValues) => {
    updateCompanyMutation.mutate(data)
  }

  if (isLoading) {
    return <FormSkeleton />
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tên công ty</CardTitle>
        <CardDescription className="mt-2">
          Mặc định, tên nhóm sẽ được hiển thị trên hóa đơn của bạn. Nếu bạn muốn hiển thị một tên tùy chỉnh thay thế,
          vui lòng nhập vào đây.
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4">
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="full_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên đầy đủ</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      maxLength={64}
                      placeholder="CTY TNHH ABC"
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <p className="text-muted-foreground text-xs">Nhập tối đa 64 ký tự.</p>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="short_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên viết tắt</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      maxLength={64}
                      placeholder="Viettel, Vingroup, Vnpt"
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <p className="text-muted-foreground text-xs">Nhập tối đa 64 ký tự.</p>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              variant="default"
              size="sm"
              disabled={updateCompanyMutation.isPending}>
              {updateCompanyMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
