'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import FormSkeleton from '@/components/display/FormSkeleton'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import useAuth from '@/lib/hooks/useAuth'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

const billingAddressSchema = z.object({
  billing_address_line1: z.string().max(255, 'Địa chỉ dòng 1 không được vượt quá 255 ký tự').optional(),
  billing_address_line2: z.string().max(255, 'Địa chỉ dòng 2 không được vượt quá 255 ký tự').optional(),
  billing_city: z.string().max(255, 'Thành phố không được vượt quá 255 ký tự').optional(),
  billing_state: z.string().max(255, 'Tỉnh/Bang không được vượt quá 255 ký tự').optional(),
  billing_postal_code: z.string().max(20, 'Mã bưu điện không được vượt quá 20 ký tự').optional(),
  billing_country: z.string().max(255, 'Quốc gia không được vượt quá 255 ký tự').optional(),
})

type BillingAddressValues = z.infer<typeof billingAddressSchema>

export function BillingAddressForm() {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()

  // Fetch company data
  const { data: company, isLoading } = useQuery({
    queryKey: ['company', user?.current_organization?.id],
    queryFn: async () => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/company`)
    },
    enabled: !!user?.current_organization?.id && !authLoading,
  })

  // Update company mutation
  const updateCompanyMutation = useMutation({
    mutationFn: async (data: BillingAddressValues) => {
      if (!user?.current_organization?.id) {
        throw new Error('Không tìm thấy thông tin tổ chức')
      }
      return queryFetchHelper(`/${user.current_organization.id}/company`, {
        method: 'PUT',
        body: JSON.stringify(data),
      })
    },
    onMutate: () => {
      toast.loading('Đang cập nhật địa chỉ liên hệ...', { id: 'billing-address-update' })
    },
    onSuccess: (response: any) => {
      toast.success(response.message || 'Cập nhật địa chỉ liên hệ thành công', { id: 'billing-address-update' })
      queryClient.invalidateQueries({
        queryKey: ['company', user?.current_organization?.id],
      })
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Có lỗi xảy ra khi cập nhật địa chỉ liên hệ', { id: 'billing-address-update' })
    },
  })

  const form = useForm<BillingAddressValues>({
    resolver: zodResolver(billingAddressSchema),
    defaultValues: {
      billing_address_line1: '',
      billing_address_line2: '',
      billing_city: '',
      billing_state: '',
      billing_postal_code: '',
      billing_country: '',
    },
    mode: 'onChange',
  })

  // Update form when company data is loaded
  useEffect(() => {
    if (company?.data) {
      form.reset({
        billing_address_line1: company.data.billing_address_line1 || '',
        billing_address_line2: company.data.billing_address_line2 || '',
        billing_city: company.data.billing_city || '',
        billing_state: company.data.billing_state || '',
        billing_postal_code: company.data.billing_postal_code || '',
        billing_country: company.data.billing_country || '',
      })
    }
  }, [company, form])

  const onSubmit = (data: BillingAddressValues) => {
    updateCompanyMutation.mutate(data)
  }

  if (isLoading) {
    return <FormSkeleton />
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Địa chỉ liên hệ</CardTitle>
        <CardDescription>Nếu bạn muốn thêm địa chỉ gửi hóa đơn, vui lòng nhập vào đây.</CardDescription>
      </CardHeader>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4">
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="billing_address_line1"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Địa chỉ dòng 1</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="billing_address_line2"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Địa chỉ dòng 2</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="billing_city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Xã/Phường</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Hoc Mon"
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="billing_state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tỉnh/Thành phố</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        disabled={updateCompanyMutation.isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="billing_postal_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã bưu điện</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="700000"
                        disabled={updateCompanyMutation.isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="billing_country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quốc gia</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Viet Nam"
                      disabled={updateCompanyMutation.isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              variant="default"
              size="sm"
              disabled={updateCompanyMutation.isPending}>
              {updateCompanyMutation.isPending ? 'Đang cập nhật...' : 'Cập nhật'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
