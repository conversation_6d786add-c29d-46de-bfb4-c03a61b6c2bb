'use client'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BillingAddressForm } from './components/billing-address-form'
import { CompanyNameForm } from './components/company-name-form'
import { CompanySummary } from './components/company-summary'
import { PaymentInfoForm } from './components/payment-info-form'
import { TaxIdForm } from './components/tax-id-form'
import { OrganizationBrandingForm } from './organization/organization-branding-form'

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <PageHeading
        title="Thiết lập tổ chức"
        description="Quản lý thông tin tổ chức và hồ sơ công ty của bạn"
      />

      <Tabs
        defaultValue="organization"
        className="space-y-6">
        <TabsList>
          <TabsTrigger value="organization">Thông tin tổ chức</TabsTrigger>
          <TabsTrigger value="company"><PERSON><PERSON> sơ công ty</TabsTrigger>
          <TabsTrigger value="payment">Thông tin thanh toán</TabsTrigger>
        </TabsList>

        <TabsContent
          value="organization"
          className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Thông tin tổ chức</h2>
            <p className="text-muted-foreground text-sm">
              Quản lý thông tin thương hiệu và branding cho tổ chức của bạn
            </p>
          </div>
          <OrganizationBrandingForm />
        </TabsContent>

        <TabsContent
          value="company"
          className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Hồ sơ công ty</h2>
            <p className="text-muted-foreground text-sm">Cấu hình thông tin cơ bản về tài khoản công ty của bạn</p>
          </div>

          <div className="flex max-w-1/2 flex-col justify-between gap-4">
            <CompanyNameForm />
            <BillingAddressForm />
            <TaxIdForm />
          </div>
        </TabsContent>

        <TabsContent
          value="payment"
          className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Thông tin thanh toán</h2>
            <p className="text-muted-foreground text-sm">
              Quản lý thông tin ngân hàng và thanh toán cho tổ chức của bạn
            </p>
          </div>
          <PaymentInfoForm />
        </TabsContent>
      </Tabs>
    </div>
  )
}
