<?php

namespace Modules\Organization\Data;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON>tie\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
class CompanyData extends Data
{
    public function __construct(
        public string $id,
        public string $organization_id,
        public ?string $full_name,
        public ?string $short_name,
        public ?string $billing_address_line1,
        public ?string $billing_address_line2,
        public ?string $billing_city,
        public ?string $billing_state,
        public ?string $billing_postal_code,
        public ?string $billing_country,
        public ?string $tax_id,
        public string $created_at,
        public string $updated_at,
    ) {
        // Computed properties
        $this->display_company_name = $this->full_name ?: $this->short_name;

        $addressParts = array_filter([
            $this->billing_address_line1,
            $this->billing_address_line2,
            $this->billing_city,
            $this->billing_state,
            $this->billing_postal_code,
            $this->billing_country,
        ]);
        $this->formatted_billing_address = !empty($addressParts) ? implode(', ', $addressParts) : null;

        $this->has_complete_billing_address = !empty($this->billing_address_line1) &&
                                            !empty($this->billing_city) &&
                                            !empty($this->billing_country);

        $this->has_company_name = !empty($this->full_name) || !empty($this->short_name);
    }

    // Computed properties
    public ?string $display_company_name;
    public ?string $formatted_billing_address;
    public bool $has_complete_billing_address;
    public bool $has_company_name;
}
