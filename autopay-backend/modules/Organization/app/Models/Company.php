<?php

namespace Modules\Organization\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Core\Models\Concerns\HasSchemalessAttributes;

/**
 * Company Model
 *
 * Stores company information for organizations including:
 * - Company names (full and short)
 * - Billing address details
 * - Tax identification information
 */
class Company extends Model
{
    use HasFactory, HasSchemalessAttributes, HasUlids;

    protected $table = 'companies';

    protected $guarded = [];

    protected $casts = [
        'data' => 'object',
    ];

    /**
     * Get the organization that owns this company profile
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * Get full billing address as formatted string
     */
    public function getFormattedBillingAddressAttribute(): ?string
    {
        $addressParts = array_filter([
            $this->billing_address_line1,
            $this->billing_address_line2,
            $this->billing_city,
            $this->billing_state,
            $this->billing_postal_code,
            $this->billing_country,
        ]);

        return !empty($addressParts) ? implode(', ', $addressParts) : null;
    }

    /**
     * Check if company profile has complete billing address
     */
    public function hasCompleteBillingAddress(): bool
    {
        return !empty($this->billing_address_line1) &&
               !empty($this->billing_city) &&
               !empty($this->billing_country);
    }

    /**
     * Check if company has company name information
     */
    public function hasCompanyName(): bool
    {
        return !empty($this->full_name) || !empty($this->short_name);
    }

    /**
     * Get display company name (prefer full name, fallback to short name)
     */
    public function getDisplayCompanyNameAttribute(): ?string
    {
        return $this->full_name ?: $this->short_name;
    }
}
