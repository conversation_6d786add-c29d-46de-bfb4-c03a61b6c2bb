<?php

namespace Modules\Organization\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Data\CompanyData;
use Modules\Organization\Models\Company;
use Symfony\Component\HttpFoundation\Response;

class CompanyController extends Controller
{
    /**
     * Get company for current organization
     */
    public function show(): Response
    {
        $organization = auth()->user()->currentOrganization;

        if (!$organization) {
            return ResponseHelper::error(
                message: 'Không tìm thấy tổ chức hiện tại',
                httpCode: 404
            );
        }

        $company = $organization->company;

        if (!$company) {
            // Create empty company if not exists
            $company = Company::create([
                'organization_id' => $organization->id,
            ]);
        }

        return ResponseHelper::success(
            message: 'Lấy thông tin công ty thành công',
            data: CompanyData::from($company)
        );
    }

    /**
     * Update company for current organization
     */
    public function update(Request $request): Response
    {
        $organization = auth()->user()->currentOrganization;

        if (!$organization) {
            return ResponseHelper::error(
                message: '<PERSON>hông tìm thấy tổ chức hiện tại',
                httpCode: 404
            );
        }

        $validated = $request->validate([
            'full_name' => 'nullable|string|max:64',
            'short_name' => 'nullable|string|max:64',
            'billing_address_line1' => 'nullable|string|max:255',
            'billing_address_line2' => 'nullable|string|max:255',
            'billing_city' => 'nullable|string|max:255',
            'billing_state' => 'nullable|string|max:255',
            'billing_postal_code' => 'nullable|string|max:20',
            'billing_country' => 'nullable|string|max:255',
            'tax_id' => 'nullable|string|max:50',
        ]);

        $company = $organization->company;

        if (!$company) {
            $company = Company::create([
                'organization_id' => $organization->id,
                ...$validated
            ]);
        } else {
            $company->update($validated);
        }

        return ResponseHelper::success(
            message: 'Cập nhật thông tin công ty thành công',
            data: CompanyData::from($company->fresh())
        );
    }
}
