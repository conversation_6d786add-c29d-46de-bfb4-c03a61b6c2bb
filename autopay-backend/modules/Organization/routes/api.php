<?php

use Illuminate\Support\Facades\Route;
use Modules\Organization\Http\Controllers\CompanyController;
use Modules\Organization\Http\Controllers\DomainController;
use Modules\Organization\Http\Controllers\InvitationController;
use Modules\Organization\Http\Controllers\OrganizationController;
use Modules\Organization\Http\Controllers\PermissionController;
use Modules\Organization\Http\Controllers\RoleController;

// Public domain configuration endpoint
Route::get('/domains/config', [DomainController::class, 'getConfig']);

// Organization listing and creation routes
Route::group([
    'controller' => OrganizationController::class,
    'prefix' => 'organizations',
    'as' => 'organizations.',
    'middleware' => ['auth:sanctum', '2fa'],
], static function (): void {
    Route::get('/', 'index')->name('index');
    Route::post('/', 'store')->name('store');
});

// Organization-specific routes with a pattern {organization}
Route::group([
    'controller' => OrganizationController::class,
    'prefix' => '{organization}',
    'as' => 'organizations.',
    'middleware' => 'organization.standard',
], static function (): void {
    Route::post('/switch', 'switch')->name('switch');
    Route::post('/leave', 'leave')->name('leave');

    // Organization data routes (with cache)
    Route::get('/dashboard', 'dashboard')->name('dashboard')
        ->middleware('organization.permission:organization:instance:view');

    Route::get('/my-teams', 'myTeams')->name('my-teams')
        ->middleware('organization.permission:team:instance:view');
    Route::get('/roles', 'roles')->name('roles')
        ->middleware('organization.permission:role:instance:view');
    Route::get('/resources', 'resources')->name('resources')
        ->middleware('organization.permission:organization:instance:view');

    // Organization member management
    Route::get('/members', 'members')->name('members')
        ->middleware('organization.permission:organization:instance:view');
    Route::get('/members/{member}', 'memberDetails')->name('members.details')
        ->middleware('organization.permission:organization:instance:view');
    Route::get('/members/search', 'searchMembers')->name('members.search')
        ->middleware('organization.permission:organization:instance:view');

    // Search users by email to invite to the organization
    Route::get('/users', 'users')->name('users')
        ->middleware(['throttle:10,1', 'organization.permission:organization:member:manage']);

    // Routes that require an organization owner
    Route::group([
        'middleware' => 'organization.owner',
    ], static function (): void {
        Route::patch('/', 'update')->name('update')
            ->middleware('organization.permission:organization:instance:update');
        Route::delete('/', 'destroy')->name('destroy')
            ->middleware('organization.permission:organization:instance:admin');
    });

    // Routes that require organization member with permissions
    Route::group([], static function (): void {
        Route::delete('/members/{member}', 'removeMember')->name('members.remove')
            ->middleware('organization.permission:organization:member:remove');
        Route::patch('/members/{member}/role', 'updateMemberRole')->name('members.update-role')
            ->middleware('organization.permission:organization:member:manage');
    });

    // Invitation management routes
    Route::group([
        'controller' => InvitationController::class,
        'prefix' => '/invitations',
        'as' => 'invitations.',
    ], static function (): void {
        Route::get('/', 'invitations')->name('index')
            ->middleware('organization.permission:organization:member:invite');
        Route::post('/', 'inviteMember')->name('invite')
            ->middleware('organization.permission:organization:member:invite');
        Route::post('/bulk', 'bulkInviteMembers')->name('invite.bulk')
            ->middleware('organization.permission:organization:member:invite');
        Route::post('/{invitation}/resend', 'resendInvitation')->name('resend')
            ->middleware('organization.permission:organization:member:invite');
        Route::delete('/{invitation}', 'cancelInvitation')->name('cancel')
            ->middleware('organization.permission:organization:member:invite');
        Route::delete('/{invitation}/delete', 'deleteInvitation')->name('delete')
            ->middleware('organization.permission:organization:member:invite');
    });

    // Organization role management routes
    Route::group([
        'controller' => RoleController::class,
        'prefix' => '/roles',
        'as' => 'roles.',
    ], static function (): void {
        Route::get('/', 'index')->name('index')
            ->middleware('organization.permission:role:instance:view');
        Route::post('/', 'store')->name('store')
            ->middleware('organization.permission:role:instance:create');
        Route::get('/{role}', 'show')->name('show')
            ->middleware('organization.permission:role:instance:view');
        Route::patch('/{role}', 'update')->name('update')
            ->middleware('organization.permission:role:instance:update');
        Route::patch('/{role}/permissions', 'updatePermissions')->name('update-permissions')
            ->middleware('organization.permission:role:instance:update');
        Route::delete('/{role}', 'destroy')->name('destroy')
            ->middleware('organization.permission:role:instance:delete');
        Route::get('/{role}/members', 'members')->name('members')
            ->middleware('organization.permission:role:instance:view');
        Route::get('/{role}/permissions', 'getPermissions')->name('permissions')
            ->middleware('organization.permission:role:instance:view');
    });

    // Organization Permission Management Routes
    Route::group([
        'controller' => PermissionController::class,
        'prefix' => '/permissions',
        'as' => 'permissions.',
    ], static function (): void {
        // Admin permissions (all available permissions) - only for organization owners
        Route::get('/all', 'getAllPermissions')->name('all')
            ->middleware(['organization.owner', 'organization.permission:organization:instance:admin']);

        // Available permissions for role creation
        Route::get('/available', 'getAvailablePermissions')->name('available')
            ->middleware('organization.permission:role:instance:view');
    });

    // Organization-level user routes with a pattern {organization}/user
    Route::group([
        'controller' => PermissionController::class,
        'prefix' => '/user',
        'as' => 'organization.user.',
    ], static function (): void {
        // User permissions in the organization context (all teams)
        Route::get('/permissions', 'getUserPermissionsInOrganization')->name('permissions')
            ->middleware('organization.permission:organization:instance:view');
        // User roles in the organization context (all teams)
        Route::get('/roles', 'getUserRolesInOrganization')->name('roles')
            ->middleware('organization.permission:organization:instance:view');
    });

    // Company management routes
    Route::group([
        'controller' => CompanyController::class,
        'prefix' => '/company',
        'as' => 'company.',
    ], static function (): void {
        Route::get('/', 'show')->name('show')
            ->middleware('organization.permission:organization:instance:view');
        Route::put('/', 'update')->name('update')
            ->middleware('organization.permission:organization:instance:update');
    });

    // Domain management routes
    Route::group([
        'controller' => DomainController::class,
        'prefix' => '/domains',
        'as' => 'domains.',
    ], static function (): void {
        Route::get('/', 'index')->name('index')
            ->middleware('organization.permission:domain:instance:view');
        Route::post('/', 'store')->name('store')
            ->middleware('organization.permission:domain:instance:create');
        Route::put('/{id}', 'update')->name('update')
            ->middleware('organization.permission:domain:instance:update');
        Route::delete('/{id}', 'destroy')->name('destroy')
            ->middleware('organization.permission:domain:instance:delete');
        Route::post('/setup', 'setupDomain')->name('setup')
            ->middleware('organization.permission:domain:instance:create');
        Route::post('/upload', 'uploadFile')->name('upload')
            ->middleware('organization.permission:domain:instance:update');
        Route::post('/delete-file', 'deleteFile')->name('delete-file')
            ->middleware('organization.permission:domain:instance:update');
    });
});

// Team-specific user routes with a pattern {organization}/{team}/user
Route::group([
    'prefix' => '{organization}/{team}',
    'middleware' => 'team.standard',
], static function (): void {
    Route::group([
        'controller' => PermissionController::class,
        'prefix' => '/user',
        'as' => 'team.user.',
    ], static function (): void {
        // User permissions in the team context
        Route::get('/permissions', 'getUserPermissionsInTeam')->name('permissions')
            ->middleware('organization.permission:team:instance:view');
        // User roles in the team context
        Route::get('/roles', 'getUserRolesInTeam')->name('roles')
            ->middleware('organization.permission:team:instance:view');
    });
});

// Public invitation routes (no auth required)
Route::group([
    'controller' => InvitationController::class,
    'prefix' => 'invitations',
    'as' => 'invitations.',
], static function (): void {
    Route::get('/{token}', 'show')->name('show');
    Route::post('/{token}/accept', 'accept')->name('accept');
    Route::post('/{token}/decline', 'decline')->name('decline');
});
