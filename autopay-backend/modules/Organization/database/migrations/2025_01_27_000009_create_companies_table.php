<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', static function (Blueprint $table) {
            $table->ulid('id')->primary();

            $table->foreignUlid('organization_id')
                ->constrained('organizations')
                ->cascadeOnDelete();

            // Company name information
            $table->string('full_name', 64)->nullable();
            $table->string('short_name', 64)->nullable();

            // Billing address information
            $table->string('billing_address_line1')->nullable();
            $table->string('billing_address_line2')->nullable();
            $table->string('billing_city')->nullable();
            $table->string('billing_state')->nullable();
            $table->string('billing_postal_code')->nullable();
            $table->string('billing_country')->nullable();

            // Tax information
            $table->string('tax_id')->nullable();

            // Additional data field for future extensibility
            $table->json('data')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['organization_id']);
            $table->index(['created_at']);

            // Ensure one company per organization
            $table->unique(['organization_id'], 'companies_org_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
