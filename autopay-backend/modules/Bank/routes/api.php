<?php

use Illuminate\Support\Facades\Route;
use Modules\Bank\Http\Controllers\BankController;
use Modules\Bank\Http\Controllers\BankWebhookController;

Route::group([
    'middleware' => ['auth:sanctum'],
    'as' => 'bank.',
    'controller' => BankController::class,
], function () {
    // Banks listing and info
    Route::get('banks', 'index')->name('index');
    Route::get('banks/{bankCode}/info', 'getBankInfo')->name('info');

    // Bank operations grouped by bankCode
    Route::group(['prefix' => 'banks/{bankCode}'], function () {
        Route::post('check-account', 'checkAccountNumber')->name('check-account');
        Route::post('register', 'registerAccount')->name('register');
        Route::post('transactions', 'getTransactionHistory')->name('transactions');
        Route::post('balance', 'getAccountBalance')->name('balance');

        // Virtual Account operations
        Route::post('virtual-account', 'createVirtualAccount')->name('virtual-account.create');
        Route::delete('virtual-account', 'deleteVirtualAccount')->name('virtual-account.delete');
        Route::get('virtual-account/transactions', 'getVirtualAccountTransactions')->name('virtual-account.transactions');

        // Account linking operations
        Route::post('link-account', 'linkAccount')->name('link-account');
        Route::post('verify-account', 'verifyLinkedAccount')->name('verify-account');

        // Transaction operations
        Route::post('sync-transaction', 'syncTransaction')->name('sync-transaction');
    });
});

// Bank webhook routes (no authentication required)
Route::group([
    'prefix' => 'webhooks',
    'controller' => BankWebhookController::class,
], function () {
    // OCB webhooks
    Route::post('ocb/transaction-sync', 'ocbTransactionSync')->name('bank.webhook.ocb.transaction-sync');

    // MBBank webhooks
    Route::post('mb/token-generate', 'mbTokenGenerate')->name('bank.webhook.mb.token-generate');
    Route::post('mb/transaction-sync', 'mbTransactionSync')->name('bank.webhook.mb.transaction-sync');
    Route::post('mb/va-account', 'mbVaAccount')->name('bank.webhook.mb.va-account');
    Route::post('mb/va-transaction-sync', 'mbVaTransactionSync')->name('bank.webhook.mb.va-transaction-sync');

    // KienLongBank webhooks
    Route::post('klb/token-generate', 'klbTokenGenerate')->name('bank.webhook.klb.token-generate');
    Route::post('klb/transaction-sync', 'klbTransactionSync')->name('bank.webhook.klb.transaction-sync');
});
