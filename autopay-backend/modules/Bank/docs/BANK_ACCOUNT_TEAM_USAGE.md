# Bank Account Team Management

## Kiến trúc

Với bảng `team_resources` hiện có, bank accounts đư<PERSON><PERSON> gán cho teams thông qua polymorphic relationship:

```
Organization -> Team -> TeamResource (polymorphic) -> BankAccount
```

## Cấu trúc Database

### Bảng chính:
- `organizations`: Tổ chức
- `teams`: Teams thuộc organization
- `bank_accounts`: T<PERSON>i kho<PERSON>n ngân hàng (có `organization_id` để tối ưu truy vấn)
- `team_resources`: Gán resources cho teams (polymorphic)

### Indexes tối ưu:
```sql
-- bank_accounts
INDEX (organization_id, is_active)

-- team_resources  
INDEX (team_id, resource_type, is_active)
UNIQUE (team_id, resource_type, resource_id)
```

## API Endpoints

### 1. Thống kê (Bank Module)

```bash
# Thống kê theo organization (nhanh nhất)
GET /api/v1/bank-accounts/stats/organizations/{orgId}

# Thống kê theo team
GET /api/v1/bank-accounts/stats/teams/{teamId}

# Danh sách bank accounts của organization với filter
GET /api/v1/bank-accounts/stats/organizations/{orgId}/accounts?team_id={teamId}&bank_code=ocb

# So sánh hiệu suất truy vấn
GET /api/v1/bank-accounts/stats/organizations/{orgId}/performance
```

### 2. Quản lý Team Resources (Team Module)

```bash
# Lấy bank accounts của team
GET /api/{organization}/{team}/resources/bank-accounts

# Gán bank account cho team
POST /api/{organization}/{team}/resources/bank-accounts/assign
{
    "bank_account_id": "01JKXXX..."
}

# Gán hàng loạt bank accounts cho team
POST /api/{organization}/{team}/resources/bank-accounts/bulk-assign
{
    "bank_account_ids": ["01JKXXX...", "01JKYYY..."]
}

# Bỏ gán resource (dùng endpoint chung)
DELETE /api/{organization}/{team}/resources/{teamResourceId}
```

## Cách sử dụng trong Code

### 1. Thống kê nhanh theo Organization

```php
// Cách 1: Truy vấn trực tiếp (nhanh nhất)
$totalAccounts = BankAccount::byOrganization($orgId)->active()->count();

// Cách 2: Qua relationship
$org = Organization::find($orgId);
$stats = $org->getBankAccountsStats();

// Kết quả:
[
    'total' => 15,
    'verified' => 12,
    'unverified' => 3,
    'by_bank' => ['ocb' => 8, 'mb' => 7],
    'by_team' => ['Team A' => 5, 'Team B' => 10],
    'unassigned' => 0
]
```

### 2. Lấy bank accounts của team

```php
$team = Team::find($teamId);
$bankAccounts = $team->bankAccounts()->active()->get();

// Hoặc với thống kê
$stats = [
    'total' => $team->getResourcesCountByType(BankAccount::class),
    'accounts' => $team->bankAccounts()->with('bank')->get()
];
```

### 3. Gán bank account cho team

```php
$bankAccount = BankAccount::find($bankAccountId);
$team = Team::find($teamId);

// Kiểm tra cùng organization
if ($bankAccount->organization_id !== $team->organization_id) {
    throw new Exception('Bank account and team must belong to same organization');
}

// Gán qua trait HasTeamResources
$assignment = $bankAccount->assignToTeam($team, $user);

// Hoặc gán qua team
$assignment = $team->assignResource($bankAccount, $user);
```

### 4. Kiểm tra quyền truy cập

```php
// Kiểm tra user có quyền truy cập bank account không
$hasAccess = $bankAccount->userHasAccess($user, 'read');

// Kiểm tra bank account có được gán cho team không
$isAssigned = $bankAccount->isAssignedToTeam($team);

// Lấy teams được gán bank account
$assignedTeams = $bankAccount->assignedTeams();
```

## Hiệu suất Truy vấn

### So sánh các phương pháp:

1. **Direct organization_id query** (nhanh nhất):
   ```php
   BankAccount::byOrganization($orgId)->count(); // ~0.5ms
   ```

2. **Through relationship**:
   ```php
   $org->bankAccounts()->count(); // ~0.7ms
   ```

3. **Through team_resources** (chậm nhất):
   ```php
   // Requires multiple joins: teams -> team_resources -> bank_accounts
   $teamIds = $org->teams()->pluck('id');
   $bankAccountIds = TeamResource::whereIn('team_id', $teamIds)
       ->where('resource_type', BankAccount::class)
       ->pluck('resource_id');
   BankAccount::whereIn('id', $bankAccountIds)->count(); // ~2-5ms
   ```

### Khuyến nghị:
- **Thống kê organization**: Dùng `organization_id` trực tiếp
- **Thống kê team**: Dùng `team_resources` (không có cách khác)
- **Filter theo team**: Dùng `team_resources` với subquery

## Ví dụ Thực tế

```php
// Controller method để lấy dashboard data
public function getDashboard(Organization $org): array
{
    return [
        // Nhanh - dùng organization_id trực tiếp
        'total_bank_accounts' => $org->active_bank_accounts_count,
        'verified_accounts' => $org->verified_bank_accounts_count,
        
        // Chi tiết - dùng relationship
        'stats_by_bank' => $org->getBankAccountsStats()['by_bank'],
        
        // Team breakdown - cần join với team_resources
        'stats_by_team' => $org->getBankAccountsStats()['by_team'],
        
        // Recent accounts
        'recent_accounts' => $org->bankAccounts()
            ->with(['bank', 'user'])
            ->latest()
            ->limit(5)
            ->get(),
    ];
}
```

## Migration Notes

Khi chạy migration, cần:

1. Thêm `organization_id` vào `bank_accounts`
2. Populate `organization_id` từ user's current organization
3. Tạo indexes cho hiệu suất

```php
// Migration để populate organization_id
DB::statement("
    UPDATE bank_accounts ba 
    SET organization_id = (
        SELECT u.current_organization_id 
        FROM users u 
        WHERE u.id = ba.user_id
    )
    WHERE organization_id IS NULL
");
```
