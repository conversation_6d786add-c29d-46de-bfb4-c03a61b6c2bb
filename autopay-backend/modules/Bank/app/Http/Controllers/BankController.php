<?php

namespace Modules\Bank\Http\Controllers;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Modules\Bank\Exceptions\BankStrategyNotFoundException;
use Modules\Bank\Exceptions\UnsupportedBankException;
use Modules\Bank\Factories\BankStrategyFactory;
use Modules\Core\Helpers\ResponseHelper;
use Symfony\Component\HttpFoundation\Response;

class BankController extends Controller
{
    /**
     * Get a list of supported banks
     */
    public function index(): Response
    {
        try {
            $supportedBanks = BankStrategyFactory::getAllBanksConfig();

            $banks = collect($supportedBanks)->map(function ($config, $code) {
                return [
                    'code' => $code,
                    'name' => $config['name'],
                    'display_name' => $config['display_name'],
                    'is_active' => $config['is_active'],
                    'connection_fields' => $config['connection_fields'],
                    'features' => $config['features'],
                    'supports' => $config['supports'],
                ];
            })->values();

            return ResponseHelper::success('Danh sách ngân hàng được hỗ trợ.', $banks);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy danh sách ngân hàng.', null, 500);
        }
    }

    /**
     * Check account number validity
     */
    public function checkAccountNumber(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'accountNumber' => 'required|string|min:6|max:20',
            'id_card_number' => 'sometimes|string|min:9|max:12',
            'phone_number' => 'sometimes|string|min:10|max:11',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            $additionalData = [];
            if ($request->has('id_card_number')) {
                $additionalData['idCardNumber'] = $request->input('id_card_number');
            }
            if ($request->has('phone_number')) {
                $additionalData['phoneNumber'] = $request->input('phone_number');
            }

            Log::info("Checking account number for bank: {$bankCode}", [
                'account_number' => $request->input('account_number'),
                'additional_data' => $additionalData,
            ]);

            $result = $strategy->checkAccountNumber(
                $request->input('account_number'),
                $additionalData
            );

            if ($result->success) {
                return ResponseHelper::success($result->message, $result->toArray());
            }

            return ResponseHelper::error($result->message, ['error_code' => $result->errorCode], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình kiểm tra tài khoản.', null, 500);
        }
    }

    /**
     * Register an account for banking services
     */
    public function registerAccount(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|string|min:6|max:20',
            'account_name' => 'sometimes|string|max:255',
            'id_card_number' => 'sometimes|string|min:9|max:12',
            'phone_number' => 'sometimes|string|min:10|max:11',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            Log::info("Registering account for bank: {$bankCode}", [
                'account_data' => $request->all(),
            ]);

            $result = $strategy->registerAccount($request->all());

            if ($result->success) {
                return ResponseHelper::success($result->message, $result->toArray());
            }

            return ResponseHelper::error($result->message, ['error_code' => $result->errorCode], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình đăng ký tài khoản.', null, 500);
        }
    }

    /**
     * Get transaction history for an account
     */
    public function getTransactionHistory(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|string|min:6|max:20',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'limit' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            $filters = $request->only(['start_date', 'end_date', 'limit', 'page']);

            Log::info("Getting transaction history for bank: {$bankCode}", [
                'account_number' => $request->input('account_number'),
                'filters' => $filters,
            ]);

            $result = $strategy->getTransactionHistory(
                $request->input('account_number'),
                $filters
            );

            if ($result->success) {
                return ResponseHelper::success($result->message, $result->toArray());
            }

            return ResponseHelper::error($result->message, ['error_code' => $result->errorCode], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình lấy lịch sử giao dịch.', null, 500);
        }
    }

    /**
     * Create a virtual account
     */
    public function createVirtualAccount(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|string|min:6|max:20',
            'virtual_account_name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:500',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            if (! $strategy->supportsFeature('virtual_account')) {
                return ResponseHelper::error('Ngân hàng này không hỗ trợ tạo tài khoản ảo.', null, 400);
            }

            Log::info("Creating virtual account for bank: {$bankCode}", [
                'account_data' => $request->all(),
            ]);

            $result = $strategy->createVirtualAccount($request->all());

            if ($result['success']) {
                return ResponseHelper::success($result['message'], $result);
            }

            return ResponseHelper::error($result['message'], ['error_code' => $result['error_code'] ?? null], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình tạo tài khoản ảo.', null, 500);
        }
    }

    /**
     * Get account balance
     */
    public function getAccountBalance(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|string|min:6|max:20',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            if (! $strategy->supportsFeature('balance_check')) {
                return ResponseHelper::error('Ngân hàng này không hỗ trợ kiểm tra số dư.', null, 400);
            }

            Log::info("Getting account balance for bank: {$bankCode}", [
                'account_number' => $request->input('account_number'),
            ]);

            $balance = $strategy->getAccountBalance($request->input('account_number'));

            if ($balance !== null) {
                return ResponseHelper::success('Lấy số dư tài khoản thành công.', [
                    'account_number' => $request->input('account_number'),
                    'balance' => $balance,
                    'formatted_balance' => number_format($balance, 0, ',', '.').' VND',
                ]);
            }

            return ResponseHelper::error('Không thể lấy số dư tài khoản.', null, 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            Log::error("Error getting account balance for bank: {$bankCode}", [
                'error' => $e->getMessage(),
                'account_number' => $request->input('account_number'),
            ]);

            return ResponseHelper::error('Có lỗi xảy ra trong quá trình lấy số dư tài khoản.', null, 500);
        }
    }

    /**
     * Delete virtual account
     */
    public function deleteVirtualAccount(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'order' => 'sometimes|integer|min:1',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            if (! $strategy->supportsFeature('virtual_account')) {
                return ResponseHelper::error('Ngân hàng này không hỗ trợ tài khoản ảo.', null, 400);
            }

            Log::info("Deleting virtual account for bank: {$bankCode}", [
                'account_data' => $request->all(),
            ]);

            $result = $strategy->deleteVirtualAccount($request->all());

            if ($result['success']) {
                return ResponseHelper::success($result['message'], $result);
            }

            return ResponseHelper::error($result['message'], ['error_code' => $result['error_code'] ?? null], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình xóa tài khoản ảo.', null, 500);
        }
    }

    /**
     * Get virtual account transactions
     */
    public function getVirtualAccountTransactions(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'order' => 'sometimes|integer|min:1',
            'page' => 'sometimes|integer|min:0',
            'size' => 'sometimes|integer|min:1|max:500',
            'bank_account_no' => 'sometimes|string',
            'from_date' => 'sometimes|date_format:Y-m-d H:i:s',
            'to_date' => 'sometimes|date_format:Y-m-d H:i:s|after_or_equal:from_date',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            if (! $strategy->supportsFeature('virtual_account')) {
                return ResponseHelper::error('Ngân hàng này không hỗ trợ tài khoản ảo.', null, 400);
            }

            $filters = $request->only(['order', 'page', 'size', 'bank_account_no', 'from_date', 'to_date']);

            Log::info("Getting virtual account transactions for bank: {$bankCode}", [
                'filters' => $filters,
            ]);

            $result = $strategy->getVirtualAccountTransactions($filters);

            if ($result['success']) {
                return ResponseHelper::success($result['message'], $result);
            }

            return ResponseHelper::error($result['message'], ['error_code' => $result['error_code'] ?? null], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo.', null, 500);
        }
    }

    /**
     * Link account to banking services
     */
    public function linkAccount(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|string|min:6|max:20',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            Log::info("Linking account for bank: {$bankCode}", [
                'account_number' => $request->input('account_number'),
            ]);

            $result = $strategy->linkAccount($request->input('account_number'));

            if ($result['success']) {
                return ResponseHelper::success($result['message'], $result);
            }

            return ResponseHelper::error($result['message'], ['error_code' => $result['error_code'] ?? null], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình liên kết tài khoản.', null, 500);
        }
    }

    /**
     * Verify linked an account with OTP
     */
    public function verifyLinkedAccount(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string',
            'otp' => 'required|string|min:4|max:10',
            'account_number' => 'required|string|min:6|max:20',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            Log::info("Verifying linked account for bank: {$bankCode}", [
                'verification_data' => $request->all(),
            ]);

            $result = $strategy->verifyLinkedAccount($request->all());

            if ($result['success']) {
                return ResponseHelper::success($result['message'], $result);
            }

            return ResponseHelper::error($result['message'], ['error_code' => $result['error_code'] ?? null], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình xác thực tài khoản.', null, 500);
        }
    }

    /**
     * Sync transaction with a bank
     */
    public function syncTransaction(Request $request, string $bankCode): Response
    {
        $validator = Validator::make($request->all(), [
            'transaction_id' => 'required|string',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error('Dữ liệu không hợp lệ.', $validator->errors(), 422);
        }

        try {
            $strategy = BankStrategyFactory::create(strtolower($bankCode));

            // Check if strategy has syncTransaction method
            if (! method_exists($strategy, 'syncTransaction')) {
                return ResponseHelper::error('Ngân hàng này không hỗ trợ đồng bộ giao dịch.', ['error_code' => 'FEATURE_NOT_SUPPORTED'], 400);
            }

            Log::info("Syncing transaction for bank: {$bankCode}", [
                'transaction_data' => $request->all(),
            ]);

            $result = $strategy->syncTransaction($request->all());

            if ($result['success']) {
                return ResponseHelper::success($result['message'], $result);
            }

            return ResponseHelper::error($result['message'], ['error_code' => $result['error_code'] ?? null], 400);

        } catch (UnsupportedBankException $e) {
            return ResponseHelper::error('Hệ thống chưa hỗ trợ cho ngân hàng này.', null, 400);
        } catch (BankStrategyNotFoundException $e) {
            return ResponseHelper::error('Hệ thống chưa triển khai dịch vụ cho ngân hàng này.', null, 400);
        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra trong quá trình đồng bộ giao dịch.', null, 500);
        }
    }

    /**
     * Get bank configuration and features
     */
    public function getBankInfo(string $bankCode): Response
    {
        try {
            $bankConfig = BankStrategyFactory::getBankConfig($bankCode);

            if (! $bankConfig) {
                return ResponseHelper::error('Ngân hàng không được hỗ trợ.', null, 404);
            }

            return ResponseHelper::success('Thông tin ngân hàng.', [
                'code' => $bankCode,
                'name' => $bankConfig['name'],
                'display_name' => $bankConfig['display_name'],
                'is_active' => $bankConfig['is_active'],
                'connection_fields' => $bankConfig['connection_fields'],
                'features' => $bankConfig['features'],
                'supports' => $bankConfig['supports'],
            ]);

        } catch (Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy thông tin ngân hàng.', null, 500);
        }
    }
}
