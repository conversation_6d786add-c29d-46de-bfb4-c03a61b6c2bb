<?php

namespace Modules\Bank\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;
use Symfony\Component\HttpFoundation\Response;

/**
 * Bank Configuration Controller
 *
 * Handles API requests for bank configuration data
 */
class BankConfigController extends Controller
{
    /**
     * Get supported banks configuration
     *
     * @return Response
     */
    public function getSupportedBanks(): Response
    {
        $banksConfig = config('bank.supported_banks', []);
        $connectionFields = config('bank.connection_fields', []);

        $banks = [];

        foreach ($banksConfig as $code => $config) {
            if (!$config['is_active']) {
                continue;
            }

            $banks[] = [
                'code' => $code,
                'name' => $config['name'],
                'display_name' => $config['display_name'],
                'connection_fields' => $config['connection_fields'],
                'features' => $config['features'],
                'supports' => $config['supports'],
                'ui_config' => $config['ui_config'] ?? [],
            ];
        }

        return ResponseHelper::success(
            message: 'Banks configuration retrieved successfully',
            data: [
                'banks' => $banks,
                'connection_fields' => $connectionFields,
            ]
        );
    }

    /**
     * Get specific bank configuration
     *
     * @param string $bankCode
     * @return Response
     */
    public function getBankConfig(string $bankCode): Response
    {
        $banksConfig = config('bank.supported_banks', []);

        if (!isset($banksConfig[$bankCode])) {
            return ResponseHelper::error('Bank not found', null, 404);
        }

        $config = $banksConfig[$bankCode];

        if (!$config['is_active']) {
            return ResponseHelper::error('Bank is not active', null, 400);
        }

        $connectionFields = config('bank.connection_fields', []);
        $bankConnectionFields = [];

        foreach ($config['connection_fields'] as $fieldName) {
            if (isset($connectionFields[$fieldName])) {
                $bankConnectionFields[] = $connectionFields[$fieldName];
            }
        }

        return ResponseHelper::success(
            message: 'Bank configuration retrieved successfully',
            data: [
                'code' => $bankCode,
                'name' => $config['name'],
                'display_name' => $config['display_name'],
                'connection_fields' => $config['connection_fields'],
                'connection_fields_config' => $bankConnectionFields,
                'features' => $config['features'],
                'supports' => $config['supports'],
                'ui_config' => $config['ui_config'] ?? [],
            ]
        );
    }
}
