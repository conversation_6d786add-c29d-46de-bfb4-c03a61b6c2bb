<?php

namespace Modules\Bank\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;

/**
 * Bank Configuration Controller
 * 
 * Handles API requests for bank configuration data
 */
class BankConfigController extends Controller
{
    /**
     * Get supported banks configuration
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSupportedBanks(): \Illuminate\Http\JsonResponse
    {
        $banksConfig = config('bank.supported_banks', []);
        $connectionFields = config('bank.connection_fields', []);
        
        $banks = [];
        
        foreach ($banksConfig as $code => $config) {
            if (!$config['is_active']) {
                continue;
            }
            
            $banks[] = [
                'code' => $code,
                'name' => $config['name'],
                'display_name' => $config['display_name'],
                'connection_fields' => $config['connection_fields'],
                'features' => $config['features'],
                'supports' => $config['supports'],
                'ui_config' => $config['ui_config'] ?? [],
            ];
        }
        
        return ResponseHelper::success([
            'banks' => $banks,
            'connection_fields' => $connectionFields,
        ], 'Banks configuration retrieved successfully');
    }
    
    /**
     * Get specific bank configuration
     * 
     * @param string $bankCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBankConfig(string $bankCode): \Illuminate\Http\JsonResponse
    {
        $banksConfig = config('bank.supported_banks', []);
        
        if (!isset($banksConfig[$bankCode])) {
            return ResponseHelper::error('Bank not found', 404);
        }
        
        $config = $banksConfig[$bankCode];
        
        if (!$config['is_active']) {
            return ResponseHelper::error('Bank is not active', 400);
        }
        
        $connectionFields = config('bank.connection_fields', []);
        $bankConnectionFields = [];
        
        foreach ($config['connection_fields'] as $fieldName) {
            if (isset($connectionFields[$fieldName])) {
                $bankConnectionFields[] = $connectionFields[$fieldName];
            }
        }
        
        return ResponseHelper::success([
            'code' => $bankCode,
            'name' => $config['name'],
            'display_name' => $config['display_name'],
            'connection_fields' => $config['connection_fields'],
            'connection_fields_config' => $bankConnectionFields,
            'features' => $config['features'],
            'supports' => $config['supports'],
            'ui_config' => $config['ui_config'] ?? [],
        ], 'Bank configuration retrieved successfully');
    }
}
