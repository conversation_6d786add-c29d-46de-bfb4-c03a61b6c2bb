<?php

namespace Modules\Bank\Services;

/**
 * Bank Configuration Service
 * 
 * Provides methods to access bank configuration data
 */
class BankConfigService
{
    /**
     * Get all supported banks configuration
     * 
     * @param bool $activeOnly
     * @return array
     */
    public function getSupportedBanks(bool $activeOnly = true): array
    {
        $banksConfig = config('bank.supported_banks', []);
        
        if (!$activeOnly) {
            return $banksConfig;
        }
        
        return array_filter($banksConfig, function ($config) {
            return $config['is_active'] ?? false;
        });
    }
    
    /**
     * Get specific bank configuration
     * 
     * @param string $bankCode
     * @return array|null
     */
    public function getBankConfig(string $bankCode): ?array
    {
        $banksConfig = config('bank.supported_banks', []);
        
        return $banksConfig[$bankCode] ?? null;
    }
    
    /**
     * Get connection fields configuration
     * 
     * @return array
     */
    public function getConnectionFields(): array
    {
        return config('bank.connection_fields', []);
    }
    
    /**
     * Get connection fields for specific bank
     * 
     * @param string $bankCode
     * @return array
     */
    public function getBankConnectionFields(string $bankCode): array
    {
        $bankConfig = $this->getBankConfig($bankCode);
        
        if (!$bankConfig) {
            return [];
        }
        
        $connectionFields = $this->getConnectionFields();
        $bankConnectionFields = [];
        
        foreach ($bankConfig['connection_fields'] as $fieldName) {
            if (isset($connectionFields[$fieldName])) {
                $bankConnectionFields[] = $connectionFields[$fieldName];
            }
        }
        
        return $bankConnectionFields;
    }
    
    /**
     * Check if bank supports a specific feature
     * 
     * @param string $bankCode
     * @param string $feature
     * @return bool
     */
    public function bankSupportsFeature(string $bankCode, string $feature): bool
    {
        $bankConfig = $this->getBankConfig($bankCode);
        
        if (!$bankConfig) {
            return false;
        }
        
        return in_array($feature, $bankConfig['features'] ?? []);
    }
    
    /**
     * Get bank strategy class
     * 
     * @param string $bankCode
     * @return string|null
     */
    public function getBankStrategyClass(string $bankCode): ?string
    {
        $bankConfig = $this->getBankConfig($bankCode);
        
        return $bankConfig['strategy_class'] ?? null;
    }
    
    /**
     * Get banks formatted for frontend
     * 
     * @return array
     */
    public function getBanksForFrontend(): array
    {
        $banksConfig = $this->getSupportedBanks();
        $connectionFields = $this->getConnectionFields();
        
        $banks = [];
        
        foreach ($banksConfig as $code => $config) {
            $banks[] = [
                'code' => $code,
                'name' => $config['name'],
                'display_name' => $config['display_name'],
                'icon' => $config['ui_config']['icon'] ?? '',
                'speed' => $config['ui_config']['speed'] ?? '',
                'register' => $config['ui_config']['register'] ?? [],
                'virtualAccount' => $config['ui_config']['virtual_account'] ?? false,
                'virtualAccountPrefix' => $config['ui_config']['virtual_account_prefix'] ?? '',
                'promotion' => $config['ui_config']['promotion'] ?? '',
                'stability' => $config['ui_config']['stability'] ?? '',
                'connectionFields' => $config['connection_fields'],
                'introduction' => $config['ui_config']['introduction'] ?? '',
                'supports' => $config['ui_config']['supports_display'] ?? [],
                'notes' => $config['ui_config']['notes'] ?? [],
            ];
        }
        
        return [
            'banks' => $banks,
            'connectionFields' => array_values($connectionFields),
        ];
    }
}
