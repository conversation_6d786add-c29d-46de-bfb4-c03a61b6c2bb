<?php

namespace Modules\Bank\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Core\Models\Concerns\HasSchemalessAttributes;
use Modules\User\Models\User;
use Modules\Organization\Models\Organization;
use Modules\Team\Traits\HasTeamResources;

/**
 * Bank Account Model
 *
 * Represents a bank account that has been connected to the system
 */
class BankAccount extends Model
{
    use HasUlids, HasSchemalessAttributes, HasTeamResources;

    protected $table = 'bank_accounts';

    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
        'balance' => 'decimal:2',
        'last_sync_at' => 'datetime',
        'verified_at' => 'datetime',
        'account_data' => 'array',
        'data' => 'object',
    ];

    /**
     * Get the user that owns this bank account
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bank this account belongs to
     */
    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class, 'bank_code', 'code');
    }

    /**
     * Get the organization this account belongs to
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }



    /**
     * Get bank connection for this account
     */
    public function connection(): BelongsTo
    {
        return $this->belongsTo(BankConnection::class, 'bank_connection_id');
    }

    /**
     * Get transactions for this account
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(BankTransaction::class);
    }

    /**
     * Scope for active accounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for verified accounts
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for accounts by bank code
     */
    public function scopeByBank($query, string $bankCode)
    {
        return $query->where('bank_code', $bankCode);
    }

    /**
     * Scope for accounts by user
     */
    public function scopeByUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for accounts by organization
     */
    public function scopeByOrganization($query, string $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }



    /**
     * Get masked account number for display
     */
    public function getMaskedAccountNumberAttribute(): string
    {
        $accountNumber = $this->account_number;
        if (strlen($accountNumber) <= 4) {
            return $accountNumber;
        }

        return substr($accountNumber, 0, 4) . str_repeat('*', strlen($accountNumber) - 8) . substr($accountNumber, -4);
    }

    /**
     * Check if account needs verification
     */
    public function needsVerification(): bool
    {
        return !$this->is_verified;
    }

    /**
     * Mark account as verified
     */
    public function markAsVerified(): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Update last sync timestamp
     */
    public function updateLastSync(): void
    {
        $this->update(['last_sync_at' => now()]);
    }
}
