<?php

namespace Modules\Bank\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Bank\Models\Bank;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banksConfig = config('bank.supported_banks', []);
        
        foreach ($banksConfig as $code => $config) {
            Bank::updateOrCreate(
                ['code' => $code],
                [
                    'code' => $code,
                    'name' => $config['name'],
                    'display_name' => $config['display_name'],
                    'is_active' => $config['is_active'],
                    'supports_virtual_account' => $config['supports']['virtual_account'] ?? false,
                    'supports_balance_check' => $config['supports']['balance_check'] ?? false,
                    'supports_transaction_history' => $config['supports']['transaction_history'] ?? false,
                    'connection_fields' => $config['connection_fields'],
                    'features' => $config['features'],
                    'config' => $config['api_config'] ?? [],
                    'data' => [
                        'ui_config' => $config['ui_config'] ?? [],
                        'supports' => $config['supports'] ?? [],
                        'strategy_class' => $config['strategy_class'] ?? null,
                    ],
                ]
            );
        }
    }
}
