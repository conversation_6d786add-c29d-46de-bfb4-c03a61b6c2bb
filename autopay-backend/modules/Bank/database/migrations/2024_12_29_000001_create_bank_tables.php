<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create banks table
        Schema::create('banks', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->string('code')->unique(); // e.g., 'ocb', 'mb', 'klb'
            $table->string('name'); // e.g., 'OceanBank'
            $table->string('display_name'); // e.g., '<PERSON><PERSON> hàng TMCP Phương Đông (OCB)'
            $table->boolean('is_active')->default(true);
            $table->boolean('supports_virtual_account')->default(false);
            $table->boolean('supports_balance_check')->default(false);
            $table->boolean('supports_transaction_history')->default(false);
            $table->json('connection_fields')->nullable(); // Required fields for connection
            $table->json('features')->nullable(); // Supported features
            $table->json('config')->nullable(); // Bank-specific configuration
            $table->json('data')->nullable(); // Additional schemaless data
            $table->timestamps();

            $table->index(['code', 'is_active']);
        });

        // Create bank_connections table
        Schema::create('bank_connections', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->ulid('user_id');
            $table->string('bank_code');
            $table->string('connection_name')->nullable(); // User-defined name for this connection
            $table->boolean('is_active')->default(true);
            $table->boolean('is_connected')->default(false);
            $table->timestamp('last_connected_at')->nullable();
            $table->json('connection_data')->nullable(); // Connection metadata
            $table->json('credentials')->nullable(); // Encrypted credentials
            $table->json('data')->nullable(); // Additional schemaless data
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('bank_code')->references('code')->on('banks')->onDelete('cascade');
            $table->index(['user_id', 'bank_code']);
            $table->index(['bank_code', 'is_active']);
        });

        // Create bank_accounts table
        Schema::create('bank_accounts', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->ulid('user_id');
            $table->ulid('organization_id'); // Direct reference to organization for fast queries
            $table->string('bank_code');
            $table->ulid('bank_connection_id')->nullable();
            $table->string('account_number');
            $table->string('account_name')->nullable();
            $table->string('account_type')->default('checking'); // checking, savings, etc.
            $table->boolean('is_active')->default(true);
            $table->boolean('is_verified')->default(false);
            $table->decimal('balance', 15, 2)->nullable();
            $table->timestamp('last_sync_at')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->json('account_data')->nullable(); // Account metadata from bank
            $table->json('data')->nullable(); // Additional schemaless data
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('cascade');
            $table->foreign('bank_code')->references('code')->on('banks')->onDelete('cascade');
            $table->foreign('bank_connection_id')->references('id')->on('bank_connections')->onDelete('set null');
            $table->unique(['user_id', 'bank_code', 'account_number']);
            $table->index(['user_id', 'bank_code']);
            $table->index(['bank_code', 'is_active']);
            // Performance indexes for organization queries
            $table->index(['organization_id', 'is_active']);
        });

        // Create bank_transactions table
        Schema::create('bank_transactions', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->ulid('bank_account_id');
            $table->string('transaction_id')->unique(); // Bank's transaction ID
            $table->string('reference_number')->nullable(); // Bank's reference number
            $table->enum('type', ['credit', 'debit']);
            $table->decimal('amount', 15, 2);
            $table->decimal('balance', 15, 2)->nullable(); // Account balance after transaction
            $table->string('description')->nullable();
            $table->string('counterpart_account')->nullable(); // Other party's account
            $table->string('counterpart_name')->nullable(); // Other party's name
            $table->timestamp('transaction_date');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('completed');
            $table->boolean('is_processed')->default(false);
            $table->timestamp('processed_at')->nullable();
            $table->json('transaction_data')->nullable(); // Raw transaction data from bank
            $table->json('data')->nullable(); // Additional schemaless data
            $table->timestamps();

            $table->foreign('bank_account_id')->references('id')->on('bank_accounts')->onDelete('cascade');
            $table->index(['bank_account_id', 'transaction_date']);
            $table->index(['transaction_date', 'type']);
            $table->index(['status', 'is_processed']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_transactions');
        Schema::dropIfExists('bank_accounts');
        Schema::dropIfExists('bank_connections');
        Schema::dropIfExists('banks');
    }
};
