<?php

namespace Modules\Subscription\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use LucasDotVin\Soulbscription\Enums\PeriodicityType;

class CreateOrganizationPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware/policies
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $organizationId = $this->route('organization')->id;

        return [ 
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('plans', 'name')->where('organization_id', $organizationId)
            ],
            'alias' => ['nullable', 'string', 'max:255', 'regex:/^[a-z0-9_-]*$/'],
            'description' => ['nullable', 'string', 'max:1000'],
            'grace_days' => ['nullable', 'integer', 'min:0', 'max:365'],
            'periodicity' => ['nullable', 'integer', 'min:1'],
            'periodicity_type' => ['nullable', 'string', 'in:day,week,month,year'],
            'quota' => ['nullable', 'integer', 'min:0'],
            'price' => ['nullable', 'numeric', 'min:0'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên plan là bắt buộc',
            'name.max' => 'Tên plan không được vượt quá 255 ký tự',
            'name.unique' => 'Tên plan đã tồn tại trong organization này',
            'alias.max' => 'Alias không được vượt quá 255 ký tự',
            'alias.regex' => 'Alias chỉ được chứa chữ thường, số, dấu gạch dưới và dấu gạch ngang',
            'description.max' => 'Mô tả không được vượt quá 1000 ký tự',
            'grace_days.integer' => 'Số ngày gia hạn phải là số nguyên',
            'grace_days.min' => 'Số ngày gia hạn không được âm',
            'grace_days.max' => 'Số ngày gia hạn không được vượt quá 365 ngày',
            'periodicity.integer' => 'Chu kỳ phải là số nguyên',
            'periodicity.min' => 'Chu kỳ phải lớn hơn 0',
            'periodicity_type.in' => 'Loại chu kỳ phải là: day, week, month, year',
            'quota.integer' => 'Quota phải là số nguyên',
            'quota.min' => 'Quota không được âm',
            'price.numeric' => 'Giá phải là số',
            'price.min' => 'Giá không được âm',
        ];
    }
}
