<?php

namespace Modules\Team\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Modules\Database\Models\Database;
use Modules\Domain\Models\Domain;
use Modules\Organization\Models\Organization;
use Modules\Organization\Models\OrganizationTeam;
use Modules\Server\Models\Server;
use Modules\User\Models\ModelHasRole;
use Modules\User\Models\Role;
use Modules\User\Models\User;
use Modules\Webapp\Models\Webapp;
use Modules\Bank\Models\BankAccount;

class Team extends Model
{
    use HasUlids;

    protected $guarded = [];

    protected $appends = ['users_count'];

    /**
     * Boot the model
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(static function ($team) {
            if (empty($team->alias)) {
                $team->alias = static::generateUniqueAlias($team->name.'-team', $team->organization_id);
            }
        });
    }

    /**
     * Generate a unique alias for the team within an organization
     */
    public static function generateUniqueAlias(string $name, ?string $organizationId = null): string
    {
        $baseAlias = Str::slug($name);
        $alias = $baseAlias;
        $counter = 1;

        // Check if alias already exists in the organization and append a number if needed
        while (static::whereAlias($alias)->where('organization_id', $organizationId)->exists()) {
            $alias = $baseAlias.'-'.$counter;
            $counter++;
        }

        return $alias;
    }

    /**
     * Get users in this team through the model_has_roles table
     */
    public function users(): Collection
    {
        $userIds = $this->roleAssignments()
            ->where('model_type', User::class)
            ->where('is_active', true)  // Only active role assignments
            ->pluck('model_id')
            ->unique();

        return User::whereIn('id', $userIds)->get();
    }

    /**
     * Get all users in this team (including inactive)
     */
    public function allUsers(): Collection
    {
        $userIds = $this->roleAssignments()
            ->where('model_type', User::class)
            ->pluck('model_id')
            ->unique();

        return User::whereIn('id', $userIds)->get();
    }

    /**
     * Check if the user belongs to this team
     */
    public function hasUser(User $user): bool
    {
        if (! $this->organization) {
            return false;
        }

        return $this->organization->userBelongsToTeam($user, $this);
    }

    /**
     * Add a user to this team with a role
     */
    public function addUser(User $user, Role $role): void
    {
        if (! $this->organization) {
            throw new InvalidArgumentException('Team must belong to an organization');
        }

        $this->organization->addUserToTeamWithRole($user, $this, $role);
    }

    /**
     * Remove a user from this team
     */
    public function removeUser(User $user): bool
    {
        if (! $this->organization) {
            return false;
        }

        return $this->organization->removeUserFromTeam($user, $this);
    }

    /**
     * Get roles used in this team (derived from model_has_roles)
     * Note: Roles belong to organization, not team. This method shows which roles are assigned in this team.
     */
    public function roles(): Collection
    {
        $roleIds = $this->activeRoleAssignments()
            ->where('model_type', User::class)
            ->pluck('role_id')
            ->unique();

        return Role::whereIn('id', $roleIds)->get();
    }

    /**
     * Check if the team is active
     */
    public function isActive(): bool
    {
        $orgTeam = OrganizationTeam::where('organization_id', $this->organization_id)
            ->where('team_id', $this->id)
            ->first();

        return $orgTeam?->is_active ?? true;
    }

    /**
     * Check if a user has an active role in this team
     */
    public function hasActiveUser(User $user): bool
    {
        return $this->activeRoleAssignments()
            ->where('model_id', $user->id)
            ->where('model_type', User::class)
            ->exists();
    }

    /**
     * Get a user's active role in this team
     */
    public function getUserActiveRole(User $user): ?Role
    {
        $roleAssignment = $this->activeRoleAssignments()
            ->where('model_id', $user->id)
            ->where('model_type', User::class)
            ->with('role')
            ->first();

        return $roleAssignment?->role;
    }

    /**
     * Organization that the team belongs to
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * Organization team pivot relationship
     */
    public function organizationTeam(): HasMany
    {
        return $this->hasMany(OrganizationTeam::class, 'team_id');
    }

    /**
     * Get team members with their roles
     * Note: Teams are just groups, permissions come from roles
     */
    public function getMembersWithRoles(): Collection
    {
        return $this->users()->map(function ($user) {
            $roles = $user->getRolesInTeam($this);
            $role = $roles->first(); // Since we enforce 1 role per team, this is the user's role

            // Get organization_team metadata
            $orgTeam = OrganizationTeam::where('organization_id', $this->organization_id)
                ->where('team_id', $this->id)
                ->first();

            return [
                'user' => $user,
                'role' => $role,
                'roles' => $roles, // Should only have 1 role due to constraint
                'permissions' => $role ? $role->permissions : collect(),
                'team_metadata' => [
                    'is_active' => $orgTeam?->is_active ?? true,
                ],
            ];
        });
    }

    /**
     * Check if the team has any members with specific permission
     * This is for informational purposes only - permissions are role-based
     */
    public function hasAnyMemberWithPermission(string $permission): bool
    {
        return $this->getMembersWithRoles()->contains(function ($member) use ($permission) {
            return $member['role']?->hasPermissionTo($permission) ?? false;
        });
    }

    /**
     * Team resource assignments
     */
    public function teamResources(): HasMany
    {
        return $this->hasMany(TeamResource::class, 'team_id');
    }

    /**
     * Active team resource assignments
     */
    public function activeResources(): HasMany
    {
        return $this->teamResources()->where('is_active', true);
    }

    /**
     * Assign a resource to this team
     */
    public function assignResource($resource, ?User $assignedBy = null): TeamResource
    {
        return $this->teamResources()->create([
            'resource_type' => get_class($resource),
            'resource_id' => $resource->id,
            'assigned_by' => $assignedBy?->id ?? auth()->id(),
        ]);
    }

    /**
     * Remove resource assignment from a team
     */
    public function unassignResource($resource): bool
    {
        return $this->teamResources()
            ->where('resource_type', get_class($resource))
            ->where('resource_id', $resource->id)
            ->delete() > 0;
    }

    /**
     * Check if the team has access to a specific resource
     */
    public function hasAccessToResource($resource): bool
    {
        return $this->teamResources()
            ->where('resource_type', get_class($resource))
            ->where('resource_id', $resource->id)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Get all resources of a specific type assigned to this team
     */
    public function getResourcesByType(string $resourceType): Collection
    {
        return $this->teamResources()
            ->where('resource_type', $resourceType)
            ->where('is_active', true)
            ->with('resource')
            ->get()
            ->pluck('resource');
    }

    /**
     * Get query builder for resources of a specific type assigned to this team
     * This allows chaining additional query methods
     */
    public function getResourcesQueryByType(string $resourceType): Builder
    {
        $resourceIds = $this->teamResources()
            ->where('resource_type', $resourceType)
            ->where('is_active', true)
            ->pluck('resource_id');

        return $resourceType::query()->whereIn('id', $resourceIds);
    }

    /**
     * Get count of resources of a specific type assigned to this team
     */
    public function getResourcesCountByType(string $resourceType): int
    {
        return $this->teamResources()
            ->where('resource_type', $resourceType)
            ->where('is_active', true)
            ->count();
    }

    /**
     * Check if the team has any resources of a specific type
     */
    public function hasResourcesOfType(string $resourceType): bool
    {
        return $this->getResourcesCountByType($resourceType) > 0;
    }

    /**
     * Get bank accounts assigned to this team
     */
    public function bankAccounts(): Builder
    {
        return $this->getResourcesQueryByType(BankAccount::class);
    }

    /**
     * Get users count attribute
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * Get role assignments for this team
     */
    public function roleAssignments(): HasMany
    {
        return $this->hasMany(ModelHasRole::class, 'team_id');
    }

    /**
     * Get active role assignments for this team
     */
    public function activeRoleAssignments(): HasMany
    {
        return $this->roleAssignments()->where('is_active', true);
    }
}
