<?php

namespace Modules\Team\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Models\Organization;
use Modules\Team\Models\Team;
use Modules\Team\Models\TeamResource;
use Modules\User\Services\PermissionService;
use Symfony\Component\HttpFoundation\Response;

class TeamResourceController extends Controller
{
    public function __construct(
        protected PermissionService $permissionService
    ) {}

    /**
     * Get resources assigned to a team that user can manage
     */
    public function index(Request $request, Organization $organization, Team $team): Response
    {
        $user = $request->user();

        // Get team resources that user can manage
        $resources = $team->teamResources()
            ->where('is_active', true)
            ->with(['resource', 'assignedBy'])
            ->get()
            ->filter(function ($assignment) use ($user, $organization, $team) {
                // Extract resource type from class name (e.g., "Modules\Server\Models\Server" -> "server")
                $resourceTypeParts = explode('\\', $assignment->resource_type);
                $resourceType = strtolower($resourceTypeParts[1] ?? 'unknown');

                // Check if user can manage this specific resource
                $canManageResource = $this->permissionService->userHasResourcePermission(
                    $user,
                    "$resourceType:instance:manage",
                    $assignment->resource,
                    $organization
                );

                // Check if user can manage all resources of this type
                $canManageResourceType = $this->permissionService->userCanManageResourceType(
                    $user,
                    $resourceType,
                    $organization,
                    $team
                );

                // Return true if user can manage either the specific resource or the resource type
                return $canManageResource || $canManageResourceType;
            })
            ->map(function ($assignment) {
                // Extract resource type from class name
                $resourceTypeParts = explode('\\', $assignment->resource_type);
                $resourceType = strtolower($resourceTypeParts[1] ?? 'unknown');

                return [
                    'id' => $assignment->id,
                    'resource_type' => $assignment->resource_type,
                    'resource_type_name' => $resourceType,
                    'resource_id' => $assignment->resource_id,
                    'resource' => $assignment->resource,
                    'assigned_by' => $assignment->assignedBy?->name,
                    'assigned_at' => $assignment->assigned_at?->format('Y-m-d H:i:s'),
                ];
            })
            ->values(); // Reset array keys after filtering

        return ResponseHelper::success(
            data: $resources
        );
    }

    /**
     * Assign a resource to a team
     */
    public function store(Request $request, Organization $organization, Team $team): Response
    {
        $validatedData = $request->validate([
            'resource_type' => ['required', 'string'],
            'resource_id' => ['required', 'string'],
        ]);

        // Check if a resource exists
        $resourceClass = $validatedData['resource_type'];
        if (! class_exists($resourceClass)) {
            return ResponseHelper::error(
                'Invalid resource type',
                httpCode: Response::HTTP_BAD_REQUEST,
                apiCode: Response::HTTP_BAD_REQUEST
            );
        }

        $resource = $resourceClass::find($validatedData['resource_id']);
        if (! $resource) {
            return ResponseHelper::error(
                'Resource not found',
                httpCode: Response::HTTP_NOT_FOUND,
                apiCode: Response::HTTP_NOT_FOUND
            );
        }

        // Check if already assigned
        $existingAssignment = $team->teamResources()
            ->where('resource_type', $validatedData['resource_type'])
            ->where('resource_id', $validatedData['resource_id'])
            ->first();

        if ($existingAssignment) {
            return ResponseHelper::error(
                'Resource is already assigned to this team',
                httpCode: Response::HTTP_CONFLICT,
                apiCode: Response::HTTP_CONFLICT
            );
        }

        // Create assignment
        $assignment = $team->teamResources()->create([
            'resource_type' => $validatedData['resource_type'],
            'resource_id' => $validatedData['resource_id'],
            'assigned_by' => $request->user()->id,
        ]);

        return ResponseHelper::success(
            message: 'Resource assigned to team successfully',
            data: $assignment->load(['resource', 'assignedBy'])
        );
    }

    /**
     * Update team's access to a resource
     */
    public function update(Request $request, Organization $organization, Team $team, TeamResource $teamResource): Response
    {
        $validatedData = $request->validate([
            'is_active' => ['sometimes', 'boolean'],
        ]);

        $teamResource->update($validatedData);

        return ResponseHelper::success(
            message: 'Team resource access updated successfully',
            data: $teamResource->load(['resource', 'assignedBy'])
        );
    }

    /**
     * Remove resource assignment from team
     */
    public function destroy(Request $request, Organization $organization, Team $team, TeamResource $teamResource): Response
    {
        $teamResource->delete();

        return ResponseHelper::success(
            message: 'Resource unassigned from team successfully'
        );
    }

    /**
     * Get team's resource assignment details
     */
    public function show(Request $request, Organization $organization, Team $team, TeamResource $teamResource): Response
    {
        $teamResource->load(['resource', 'assignedBy']);

        $data = [
            'id' => $teamResource->id,
            'resource_type' => $teamResource->resource_type,
            'resource_id' => $teamResource->resource_id,
            'resource' => $teamResource->resource,
            'assigned_by' => $teamResource->assignedBy?->name,
            'assigned_at' => $teamResource->assigned_at?->format('Y-m-d H:i:s'),
            'is_active' => $teamResource->is_active,
        ];

        return ResponseHelper::success(
            data: $data
        );
    }

    /**
     * Get resources of a specific type assigned to a team
     */
    public function getResourcesByType(Request $request, Organization $organization, Team $team, string $resourceType): Response
    {
        try {
            // Convert resource type name to full class name
            $resourceClass = $this->getResourceClass($resourceType);
            if (!$resourceClass) {
                return ResponseHelper::error('Resource type không hợp lệ.', null, 400);
            }

            $resources = $team->teamResources()
                ->where('resource_type', $resourceClass)
                ->where('is_active', true)
                ->with(['resource', 'assignedBy:id,first_name,last_name'])
                ->get();

            $stats = [
                'total' => $resources->count(),
                'resource_type' => $resourceType,
                'team_name' => $team->name,
            ];

            return ResponseHelper::success(
                "Danh sách {$resourceType} của team.",
                [
                    'resources' => $resources,
                    'stats' => $stats,
                ]
            );

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy danh sách resources.', null, 500);
        }
    }

    /**
     * Bulk assign resources to team
     */
    public function bulkAssignResources(Request $request, Organization $organization, Team $team): Response
    {
        $validatedData = $request->validate([
            'resource_type' => 'required|string',
            'resource_ids' => 'required|array|min:1',
            'resource_ids.*' => 'string',
        ]);

        try {
            $resourceClass = $this->getResourceClass($validatedData['resource_type']);
            if (!$resourceClass) {
                return ResponseHelper::error('Resource type không hợp lệ.', null, 400);
            }

            $resourceIds = $validatedData['resource_ids'];
            $resources = $resourceClass::whereIn('id', $resourceIds)->get();

            if ($resources->count() !== count($resourceIds)) {
                return ResponseHelper::error('Một số resources không tồn tại.', null, 400);
            }

            $assignments = [];
            $skipped = [];

            foreach ($resources as $resource) {
                // Check if already assigned
                $existingAssignment = $team->teamResources()
                    ->where('resource_type', $resourceClass)
                    ->where('resource_id', $resource->id)
                    ->first();

                if ($existingAssignment) {
                    $skipped[] = $resource->id;
                    continue;
                }

                // Create assignment
                $assignment = $team->teamResources()->create([
                    'resource_type' => $resourceClass,
                    'resource_id' => $resource->id,
                    'assigned_by' => $request->user()->id,
                ]);
                $assignments[] = $assignment;
            }

            return ResponseHelper::success('Gán hàng loạt resources thành công.', [
                'assigned' => count($assignments),
                'skipped' => count($skipped),
                'assignments' => $assignments,
                'skipped_ids' => $skipped,
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi gán hàng loạt resources.', null, 500);
        }
    }

    /**
     * Convert resource type name to full class name
     */
    private function getResourceClass(string $resourceType): ?string
    {
        $resourceMap = [
            'server' => 'Modules\\Server\\Models\\Server',
            'webapp' => 'Modules\\Webapp\\Models\\Webapp',
            'database' => 'Modules\\Database\\Models\\Database',
            'domain' => 'Modules\\Domain\\Models\\Domain',
            'bankaccount' => 'Modules\\Bank\\Models\\BankAccount',
            'bank' => 'Modules\\Bank\\Models\\BankAccount', // alias
        ];

        return $resourceMap[strtolower($resourceType)] ?? null;
    }
}
