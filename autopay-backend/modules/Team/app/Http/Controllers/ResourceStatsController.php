<?php

namespace Modules\Team\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Models\Organization;
use Modules\Team\Models\Team;
use Modules\Team\Models\TeamResource;

class ResourceStatsController extends Controller
{
    /**
     * Get resource statistics for an organization
     */
    public function getOrganizationStats(Request $request, string $organizationId): Response
    {
        try {
            $organization = Organization::findOrFail($organizationId);
            
            // Get all teams in organization
            $teamIds = $organization->teams()->pluck('id');
            
            // Get resource statistics grouped by type
            $resourceStats = TeamResource::whereIn('team_id', $teamIds)
                ->where('is_active', true)
                ->selectRaw('resource_type, COUNT(*) as count')
                ->groupBy('resource_type')
                ->get()
                ->mapWithKeys(function ($item) {
                    $resourceTypeParts = explode('\\', $item->resource_type);
                    $resourceTypeName = strtolower($resourceTypeParts[1] ?? 'unknown');
                    return [$resourceTypeName => $item->count];
                });

            // Get team breakdown
            $teamStats = TeamResource::whereIn('team_id', $teamIds)
                ->where('is_active', true)
                ->with('team:id,name')
                ->get()
                ->groupBy('team_id')
                ->map(function ($resources, $teamId) {
                    $team = $resources->first()->team;
                    return [
                        'team_name' => $team->name,
                        'total_resources' => $resources->count(),
                        'by_type' => $resources->groupBy('resource_type')
                            ->mapWithKeys(function ($items, $resourceType) {
                                $resourceTypeParts = explode('\\', $resourceType);
                                $resourceTypeName = strtolower($resourceTypeParts[1] ?? 'unknown');
                                return [$resourceTypeName => $items->count()];
                            })
                    ];
                });

            $totalResources = TeamResource::whereIn('team_id', $teamIds)
                ->where('is_active', true)
                ->count();

            return ResponseHelper::success('Thống kê resources của tổ chức.', [
                'total_resources' => $totalResources,
                'by_type' => $resourceStats,
                'by_team' => $teamStats,
                'team_count' => $teamIds->count(),
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy thống kê.', null, 500);
        }
    }

    /**
     * Get resource statistics for a team
     */
    public function getTeamStats(Request $request, string $teamId): Response
    {
        try {
            $team = Team::findOrFail($teamId);
            
            $resourceStats = $team->teamResources()
                ->where('is_active', true)
                ->selectRaw('resource_type, COUNT(*) as count')
                ->groupBy('resource_type')
                ->get()
                ->mapWithKeys(function ($item) {
                    $resourceTypeParts = explode('\\', $item->resource_type);
                    $resourceTypeName = strtolower($resourceTypeParts[1] ?? 'unknown');
                    return [$resourceTypeName => $item->count];
                });

            $totalResources = $team->teamResources()
                ->where('is_active', true)
                ->count();

            return ResponseHelper::success('Thống kê resources của team.', [
                'team_name' => $team->name,
                'total_resources' => $totalResources,
                'by_type' => $resourceStats,
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy thống kê.', null, 500);
        }
    }

    /**
     * Get resources of a specific type for an organization
     */
    public function getOrganizationResourcesByType(Request $request, string $organizationId, string $resourceType): Response
    {
        try {
            $organization = Organization::findOrFail($organizationId);
            
            // Convert resource type name to full class name
            $resourceClass = $this->getResourceClass($resourceType);
            if (!$resourceClass) {
                return ResponseHelper::error('Resource type không hợp lệ.', null, 400);
            }

            $teamIds = $organization->teams()->pluck('id');
            
            $resources = TeamResource::whereIn('team_id', $teamIds)
                ->where('resource_type', $resourceClass)
                ->where('is_active', true)
                ->with(['resource', 'team:id,name', 'assignedBy:id,first_name,last_name'])
                ->paginate($request->input('per_page', 15));

            return ResponseHelper::success("Danh sách {$resourceType} của tổ chức.", $resources);

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy danh sách resources.', null, 500);
        }
    }

    /**
     * Get resources of a specific type for a team
     */
    public function getTeamResourcesByType(Request $request, string $teamId, string $resourceType): Response
    {
        try {
            $team = Team::findOrFail($teamId);
            
            // Convert resource type name to full class name
            $resourceClass = $this->getResourceClass($resourceType);
            if (!$resourceClass) {
                return ResponseHelper::error('Resource type không hợp lệ.', null, 400);
            }

            $resources = $team->teamResources()
                ->where('resource_type', $resourceClass)
                ->where('is_active', true)
                ->with(['resource', 'assignedBy:id,first_name,last_name'])
                ->paginate($request->input('per_page', 15));

            return ResponseHelper::success("Danh sách {$resourceType} của team.", $resources);

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy danh sách resources.', null, 500);
        }
    }

    /**
     * Get available resource types in the system
     */
    public function getResourceTypes(): Response
    {
        try {
            // Get distinct resource types from team_resources
            $resourceTypes = TeamResource::select('resource_type')
                ->distinct()
                ->get()
                ->map(function ($item) {
                    $resourceTypeParts = explode('\\', $item->resource_type);
                    $resourceTypeName = strtolower($resourceTypeParts[1] ?? 'unknown');
                    return [
                        'name' => $resourceTypeName,
                        'class' => $item->resource_type,
                        'display_name' => $this->getResourceTypeDisplayName($resourceTypeName),
                    ];
                })
                ->values();

            return ResponseHelper::success('Danh sách resource types.', $resourceTypes);

        } catch (\Exception $e) {
            return ResponseHelper::error('Có lỗi xảy ra khi lấy danh sách resource types.', null, 500);
        }
    }

    /**
     * Convert resource type name to full class name
     */
    private function getResourceClass(string $resourceType): ?string
    {
        $resourceMap = [
            'server' => 'Modules\\Server\\Models\\Server',
            'webapp' => 'Modules\\Webapp\\Models\\Webapp',
            'database' => 'Modules\\Database\\Models\\Database',
            'domain' => 'Modules\\Domain\\Models\\Domain',
            'bankaccount' => 'Modules\\Bank\\Models\\BankAccount',
            'bank' => 'Modules\\Bank\\Models\\BankAccount', // alias
        ];

        return $resourceMap[strtolower($resourceType)] ?? null;
    }

    /**
     * Get display name for resource type
     */
    private function getResourceTypeDisplayName(string $resourceType): string
    {
        $displayNames = [
            'server' => 'Máy chủ',
            'webapp' => 'Ứng dụng web',
            'database' => 'Cơ sở dữ liệu',
            'domain' => 'Tên miền',
            'bankaccount' => 'Tài khoản ngân hàng',
        ];

        return $displayNames[strtolower($resourceType)] ?? ucfirst($resourceType);
    }
}
