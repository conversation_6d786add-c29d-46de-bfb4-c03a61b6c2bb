<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Team\Http\Controllers\TeamController;
use Mo<PERSON>les\Team\Http\Controllers\TeamResourceController;
use Modules\Team\Http\Controllers\TeamStatusController;
use Modules\Team\Http\Controllers\ResourceStatsController;

// Team listing and creation routes (still need to be under organizations)
Route::group([
    'controller' => TeamController::class,
    'prefix' => '{organization}/teams',
    'as' => 'teams.',
    'middleware' => 'organization.standard',
], static function (): void {
    Route::get('/', 'index')->name('index')
        ->middleware('organization.permission:team:instance:view');
    Route::post('/', 'store')->name('store')
        ->middleware('organization.permission:team:instance:create');
});

// Team management routes with a pattern {organization}/{team}
Route::group([
    'prefix' => '{organization}/{team}',
    'middleware' => 'team.standard',
], static function (): void {

    // Team basic routes
    Route::group([
        'controller' => TeamController::class,
        'as' => 'teams.',
    ], static function (): void {
        Route::get('/', 'show')->name('show')
            ->middleware('organization.permission:team:instance:view');
        Route::get('/dashboard', 'dashboard')->name('dashboard')
            ->middleware('organization.permission:team:instance:view');

        Route::post('/switch', 'switch')->name('switch')
            ->middleware('organization.permission:team:instance:view');
        Route::post('/leave', 'leaveTeam')->name('leave')
            ->middleware('organization.permission:team:instance:view');

        // Team settings require team owner access
        Route::group([
            'middleware' => 'team.owner',
        ], static function (): void {
            Route::post('/transfer-ownership', 'transferOwnership')->name('transfer-ownership')
                ->middleware('organization.permission:team:instance:admin');
            Route::patch('/', 'update')->name('update')
                ->middleware('organization.permission:team:instance:update');
            Route::delete('/', 'destroy')->name('destroy')
                ->middleware('organization.permission:team:instance:delete');

            // Team members management
            Route::get('/roles', 'roles')->name('roles')
                ->middleware('organization.permission:team:instance:view');
            Route::get('/members', 'members')->name('members')
                ->middleware('organization.permission:team:instance:view');
            Route::get('/members/search', 'searchMembers')->name('members.search')
                ->middleware('organization.permission:team:instance:view');
            Route::get('/available-users', 'availableUsers')->name('available-users')
                ->middleware('organization.permission:team:member:manage');

            Route::post('/members', 'addMember')->name('add.members')
                ->middleware('organization.permission:team:member:manage');

            Route::group([
                'middleware' => 'team.access',
            ], static function (): void {
                Route::delete('/members/{user}', 'removeMember')->name('remove.members')
                    ->middleware('organization.permission:team:member:remove');
            });
        });

        // Team status management routes
        Route::group([
            'controller' => TeamStatusController::class,
            'as' => 'teams.status.',
            'middleware' => 'team.owner',
        ], static function (): void {
            // Team status
            Route::get('/status', 'getTeamStatus')->name('team.get')
                ->middleware('organization.permission:team:instance:view');
            Route::post('/disable', 'disableTeam')->name('team.disable')
                ->middleware('organization.permission:team:instance:admin');
            Route::post('/enable', 'enableTeam')->name('team.enable')
                ->middleware('organization.permission:team:instance:admin');

            // User role status in team
            Route::get('/members/{user}/status', 'getUserRoleStatus')->name('user.get')
                ->middleware('organization.permission:team:member:view');
            Route::post('/members/{user}/disable', 'disableUserRole')->name('user.disable')
                ->middleware('organization.permission:team:member:manage');
            Route::post('/members/{user}/enable', 'enableUserRole')->name('user.enable')
                ->middleware('organization.permission:team:member:manage');
        });
    });

    // Team resource management routes
    Route::group([
        'controller' => TeamResourceController::class,
        'prefix' => '/resources',
        'as' => 'teams.resources.',
    ], static function (): void {
        // List team resources - accessible by team members
        Route::get('/', 'index')->name('index')
            ->middleware('organization.permission:team:instance:view');

        // Show specific team resource - accessible by team members
        Route::get('/{teamResource}', 'show')->name('show')
            ->middleware('organization.permission:team:instance:view');

        // Resource assignment/management - Only organization owners
        Route::middleware(['organization.owner'])->group(static function (): void {
            Route::post('/', 'store')->name('store')
                ->middleware('organization.permission:organization:resource:assign');
            Route::patch('/{teamResource}', 'update')->name('update')
                ->middleware('organization.permission:organization:resource:assign');
            Route::delete('/{teamResource}', 'destroy')->name('destroy')
                ->middleware('organization.permission:organization:resource:unassign');
        });

        // Generic resource type routes
        Route::prefix('{resourceType}')->group(static function (): void {
            // Get resources of specific type assigned to team - accessible by team members
            Route::get('/', 'getResourcesByType')->name('resources.by-type')
                ->middleware('organization.permission:team:instance:view');
        });

        // Bulk resource assignment - Only organization owners
        Route::middleware(['organization.owner'])->group(static function (): void {
            Route::post('/bulk-assign', 'bulkAssignResources')->name('resources.bulk-assign')
                ->middleware('organization.permission:organization:resource:assign');
        });
    });
});

// Resource statistics routes (organization level)
Route::group([
    'controller' => ResourceStatsController::class,
    'prefix' => 'resources/stats',
    'middleware' => 'auth:sanctum',
], static function (): void {
    Route::get('organizations/{organizationId}', 'getOrganizationStats')->name('resources.stats.organization');
    Route::get('teams/{teamId}', 'getTeamStats')->name('resources.stats.team');
    Route::get('organizations/{organizationId}/{resourceType}', 'getOrganizationResourcesByType')->name('resources.stats.organization.by-type');
    Route::get('teams/{teamId}/{resourceType}', 'getTeamResourcesByType')->name('resources.stats.team.by-type');
    Route::get('types', 'getResourceTypes')->name('resources.types');
});
